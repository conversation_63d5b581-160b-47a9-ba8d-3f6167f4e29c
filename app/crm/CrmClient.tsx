
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import SalesStore from '@/lib/salesStore';
import { useTranslation } from '@/hooks/useTranslation';

interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  position: string;
  type: 'Lead' | 'Customer' | 'Prospect';
  status: 'Active' | 'Inactive' | 'Hot' | 'Cold' | 'Converted';
  source: 'Website' | 'Referral' | 'Cold Call' | 'Email' | 'Social Media' | 'Event' | 'Other';
  assignedTo: string;
  lastContact: string;
  nextFollowUp: string;
  notes: string;
  tags: string[];
  value: number;
  createdDate: string;
  activities: Activity[];
  customProperties: Record<string, any>;
  lifecycle: 'Subscriber' | 'Lead' | 'Marketing Qualified Lead' | 'Sales Qualified Lead' | 'Opportunity' | 'Customer' | 'Evangelist';
  leadScore: number;
  avatar?: string;
  socialProfiles: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

interface Activity {
  id: string;
  contactId: string;
  type: 'Call' | 'Email' | 'Meeting' | 'Note' | 'Task' | 'Quote' | 'Order' | 'Demo' | 'Proposal';
  title: string;
  description: string;
  date: string;
  status: 'Completed' | 'Pending' | 'Cancelled' | 'Scheduled';
  createdBy: string;
  createdDate: string;
  duration?: number;
  outcome?: string;
  followUpRequired?: boolean;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
}

interface Deal {
  id: string;
  contactId: string;
  title: string;
  value: number;
  stage: 'Appointment Scheduled' | 'Qualified to Buy' | 'Presentation Scheduled' | 'Decision Maker Bought-In' | 'Contract Sent' | 'Closed Won' | 'Closed Lost';
  probability: number;
  expectedCloseDate: string;
  assignedTo: string;
  createdDate: string;
  notes: string;
  dealType: 'New Business' | 'Existing Business' | 'Renewal';
  closeReason?: string;
  lostReason?: string;
  products: string[];
  priority: 'Low' | 'Medium' | 'High';
  lastActivity: string;
  daysInStage: number;
}

interface Task {
  id: string;
  title: string;
  description: string;
  type: 'Call' | 'Email' | 'Meeting' | 'Follow-up' | 'Demo' | 'Proposal' | 'Other';
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  status: 'Open' | 'In Progress' | 'Completed' | 'Cancelled';
  assignedTo: string;
  contactId?: string;
  dealId?: string;
  dueDate: string;
  createdDate: string;
  completedDate?: string;
  notes?: string;
  reminders: string[];
}

interface Company {
  id: string;
  name: string;
  domain: string;
  industry: string;
  size: string;
  type: 'Prospect' | 'Customer' | 'Partner' | 'Other';
  status: 'Active' | 'Inactive';
  revenue: number;
  employees: number;
  location: string;
  description: string;
  contacts: string[];
  deals: string[];
  owner: string;
  createdDate: string;
  lastContactDate: string;
  tags: string[];
  customProperties: Record<string, any>;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  type: 'Follow-up' | 'Welcome' | 'Proposal' | 'Thank You' | 'Re-engagement' | 'Custom';
  category: 'Sales' | 'Marketing' | 'Support';
  isActive: boolean;
  createdBy: string;
  createdDate: string;
  variables: string[];
}

export default function CrmClient() {
  const { t } = useTranslation();
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');

  // Main tabs - Added 'pipelines' to the existing tabs
  const [activeTab, setActiveTab] = useState<'dashboard' | 'contacts' | 'companies' | 'deals' | 'pipelines' | 'tasks' | 'activities' | 'reports' | 'automation'>('dashboard');

  // Data states
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);

  // Filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('All');
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [assignedFilter, setAssignedFilter] = useState<string>('All');
  const [stageFilter, setStageFilter] = useState<string>('All');
  const [priorityFilter, setPriorityFilter] = useState<string>('All');

  // Views and layouts - Added kanban view
  const [viewType, setViewType] = useState<'list' | 'board' | 'table' | 'kanban'>('table');
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Modals
  const [showAddContactModal, setShowAddContactModal] = useState(false);
  const [showAddDealModal, setShowAddDealModal] = useState(false);
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [showAddCompanyModal, setShowAddCompanyModal] = useState(false);
  const [showAddActivityModal, setShowAddActivityModal] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showSequenceModal, setShowSequenceModal] = useState(false);

  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);

  // Forms
  const [newContact, setNewContact] = useState<Partial<Contact>>({
    name: '',
    email: '',
    phone: '',
    company: '',
    position: '',
    type: 'Lead',
    status: 'Active',
    source: 'Website',
    assignedTo: 'John Smith',
    notes: '',
    tags: [],
    value: 0,
    lifecycle: 'Lead',
    leadScore: 0,
    customProperties: {},
    socialProfiles: {}
  });

  const [newDeal, setNewDeal] = useState<Partial<Deal>>({
    title: '',
    value: 0,
    stage: 'Appointment Scheduled',
    probability: 10,
    expectedCloseDate: '',
    assignedTo: 'John Smith',
    notes: '',
    dealType: 'New Business',
    products: [],
    priority: 'Medium'
  });

  const [newTask, setNewTask] = useState<Partial<Task>>({
    title: '',
    description: '',
    type: 'Call',
    priority: 'Medium',
    status: 'Open',
    assignedTo: 'John Smith',
    dueDate: '',
    reminders: []
  });

  const [newActivity, setNewActivity] = useState<Partial<Activity>>({
    title: '',
    description: '',
    type: 'Call',
    priority: 'Medium',
    status: 'Completed',
    date: '',
    createdBy: 'John Smith'
  });

  const [newCompany, setNewCompany] = useState<Partial<Company>>({
    name: '',
    domain: '',
    industry: '',
    size: '',
    type: 'Prospect',
    status: 'Active',
    revenue: 0,
    employees: 0,
    location: '',
    description: '',
    owner: 'John Smith',
    tags: [],
    customProperties: {}
  });

  // Mount detection
  useEffect(() => {
    setMounted(true);
  }, []);

  // Load navigation mode only after mount
  useEffect(() => {
    if (!mounted) return;
    
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, [mounted]);

  // Initialize form dates after mount
  useEffect(() => {
    if (!mounted) return;
    
    const currentDate = new Date().toISOString().split('T')[0];
    setNewTask(prev => ({ ...prev, dueDate: currentDate }));
    setNewActivity(prev => ({ ...prev, date: currentDate }));
  }, [mounted]);

  // Initialize HubSpot-like sample data after mount
  useEffect(() => {
    if (!mounted) return;
    
    const sampleContacts: Contact[] = [
      {
        id: 'C001',
        name: 'Alice Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Tech Solutions Inc',
        position: 'CTO',
        type: 'Customer',
        status: 'Active',
        source: 'Referral',
        assignedTo: 'John Smith',
        lastContact: '2025-01-20',
        nextFollowUp: '2025-01-27',
        notes: 'High-value customer interested in enterprise solutions. Previously purchased our starter package.',
        tags: ['VIP', 'Enterprise', 'Technology', 'Decision Maker'],
        value: 50000,
        createdDate: '2025-01-15',
        activities: [],
        customProperties: {
          'Annual Revenue': '$5M - $10M',
          'Number of Employees': '50-100',
          'Preferred Contact Method': 'Email'
        },
        lifecycle: 'Customer',
        leadScore: 95,
        avatar: 'https://readdy.ai/api/search-image?query=professional%20businesswoman%20headshot%20corporate%20portrait%20confident%20smile&width=80&height=80&seq=alice&orientation=squarish',
        socialProfiles: {
          linkedin: 'https://linkedin.com/in/alicejohnson',
          twitter: 'https://twitter.com/alice_tech'
        }
      },
      {
        id: 'C002',
        name: 'Michael Chen',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Global Enterprises',
        position: 'Procurement Manager',
        type: 'Lead',
        status: 'Hot',
        source: 'Website',
        assignedTo: 'Sarah Wilson',
        lastContact: '2025-01-19',
        nextFollowUp: '2025-01-25',
        notes: 'Submitted inquiry form yesterday. Very interested in bulk purchase options. Budget confirmed at $15K.',
        tags: ['Bulk Order', 'Hot Lead', 'Budget Confirmed'],
        value: 15000,
        createdDate: '2025-01-18',
        activities: [],
        customProperties: {
          'Budget Range': '$10K - $20K',
          'Decision Timeline': 'Next 30 days',
          'Pain Point': 'Cost reduction'
        },
        lifecycle: 'Sales Qualified Lead',
        leadScore: 85,
        avatar: 'https://readdy.ai/api/search-image?query=professional%20businessman%20headshot%20asian%20corporate%20portrait%20friendly&width=80&height=80&seq=michael&orientation=squarish',
        socialProfiles: {
          linkedin: 'https://linkedin.com/in/michaelchen'
        }
      },
      {
        id: 'C003',
        name: 'Emily Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Innovation Startup',
        position: 'Founder & CEO',
        type: 'Prospect',
        status: 'Cold',
        source: 'Event',
        assignedTo: 'Alex Johnson',
        lastContact: '2025-01-10',
        nextFollowUp: '2025-01-30',
        notes: 'Met at TechCrunch Disrupt. Early-stage startup with limited budget but high growth potential. Worth nurturing.',
        tags: ['Startup', 'Future Potential', 'Founder', 'Tech Event'],
        value: 5000,
        createdDate: '2025-01-10',
        activities: [],
        customProperties: {
          'Funding Stage': 'Series A',
          'Industry': 'FinTech',
          'Growth Stage': 'Early'
        },
        lifecycle: 'Lead',
        leadScore: 45,
        avatar: 'https://readdy.ai/api/search-image?query=professional%20businesswoman%20latina%20entrepreneur%20headshot%20confident%20startup%20founder&width=80&height=80&seq=emily&orientation=squarish',
        socialProfiles: {
          linkedin: 'https://linkedin.com/in/emilyrodriguez',
          twitter: 'https://twitter.com/emily_innovates'
        }
      },
      {
        id: 'C004',
        name: 'David Thompson',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Manufacturing Corp',
        position: 'Operations Director',
        type: 'Customer',
        status: 'Active',
        source: 'Cold Call',
        assignedTo: 'Lisa Chen',
        lastContact: '2025-01-22',
        nextFollowUp: '2025-02-01',
        notes: 'Long-term customer since 2023. Currently looking to expand their manufacturing automation setup.',
        tags: ['Manufacturing', 'Expansion', 'Long-term Customer', 'High Value'],
        value: 75000,
        createdDate: '2024-03-15',
        activities: [],
        customProperties: {
          'Customer Since': '2023',
          'Previous Purchase': '$45K',
          'Satisfaction Score': '9/10'
        },
        lifecycle: 'Customer',
        leadScore: 90,
        avatar: 'https://readdy.ai/api/search-image?query=professional%20businessman%20middle%20aged%20manufacturing%20executive%20confident%20portrait&width=80&height=80&seq=david&orientation=squarish',
        socialProfiles: {
          linkedin: 'https://linkedin.com/in/davidthompson'
        }
      }
    ];

    const sampleDeals: Deal[] = [
      {
        id: 'D001',
        contactId: 'C001',
        title: 'Enterprise Software License - Tech Solutions Inc',
        value: 50000,
        stage: 'Decision Maker Bought-In',
        probability: 85,
        expectedCloseDate: '2025-02-15',
        assignedTo: 'John Smith',
        createdDate: '2025-01-15',
        notes: 'Decision maker confirmed budget and timeline. Legal review in progress.',
        dealType: 'New Business',
        closeReason: '',
        products: ['Enterprise License', 'Support Package', 'Training'],
        priority: 'High',
        lastActivity: '2025-01-22',
        daysInStage: 7
      },
      {
        id: 'D002',
        contactId: 'C002',
        title: 'Bulk Hardware Purchase - Global Enterprises',
        value: 15000,
        stage: 'Presentation Scheduled',
        probability: 70,
        expectedCloseDate: '2025-02-28',
        assignedTo: 'Sarah Wilson',
        createdDate: '2025-01-18',
        notes: 'Presentation scheduled for next week. Procurement committee very interested.',
        dealType: 'New Business',
        products: ['Hardware Package', 'Installation'],
        priority: 'Medium',
        lastActivity: '2025-01-21',
        daysInStage: 3
      },
      {
        id: 'D003',
        contactId: 'C004',
        title: 'Manufacturing Equipment Upgrade',
        value: 75000,
        stage: 'Qualified to Buy',
        probability: 75,
        expectedCloseDate: '2025-03-15',
        assignedTo: 'Lisa Chen',
        createdDate: '2025-01-20',
        notes: 'Customer confirmed expansion plans and budget approval process.',
        dealType: 'Existing Business',
        products: ['Automation Equipment', 'Integration Services'],
        priority: 'High',
        lastActivity: '2025-01-22',
        daysInStage: 2
      }
    ];

    const sampleTasks: Task[] = [
      {
        id: 'T001',
        title: 'Follow-up call with Alice Johnson',
        description: 'Discuss contract terms and implementation timeline',
        type: 'Call',
        priority: 'High',
        status: 'Open',
        assignedTo: 'John Smith',
        contactId: 'C001',
        dealId: 'D001',
        dueDate: '2025-01-25',
        createdDate: '2025-01-22',
        notes: 'Important to address their security concerns',
        reminders: ['2025-01-24']
      },
      {
        id: 'T002',
        title: 'Send proposal to Michael Chen',
        description: 'Prepare and send detailed proposal with bulk pricing',
        type: 'Email',
        priority: 'Medium',
        status: 'In Progress',
        assignedTo: 'Sarah Wilson',
        contactId: 'C002',
        dealId: 'D002',
        dueDate: '2025-01-26',
        createdDate: '2025-01-21',
        reminders: ['2025-01-25']
      },
      {
        id: 'T003',
        title: 'Schedule demo for Innovation Startup',
        description: 'Set up product demo to showcase key features',
        type: 'Demo',
        priority: 'Medium',
        status: 'Open',
        assignedTo: 'Alex Johnson',
        contactId: 'C003',
        dueDate: '2025-01-30',
        createdDate: '2025-01-23',
        reminders: []
      }
    ];

    const sampleCompanies: Company[] = [
      {
        id: 'COMP001',
        name: 'Tech Solutions Inc',
        domain: 'techsolutions.com',
        industry: 'Technology',
        size: 'Medium',
        type: 'Customer',
        status: 'Active',
        revenue: 7500000,
        employees: 75,
        location: 'San Francisco, CA',
        description: 'Leading technology solutions provider specializing in enterprise software and cloud services.',
        contacts: ['C001'],
        deals: ['D001'],
        owner: 'John Smith',
        createdDate: '2024-03-15',
        lastContactDate: '2025-01-22',
        tags: ['Technology', 'Enterprise', 'High Value'],
        customProperties: {
          'Website': 'https://techsolutions.com',
          'Founded': '2018',
          'Funding': 'Series B'
        }
      },
      {
        id: 'COMP002',
        name: 'Global Enterprises',
        domain: 'globalenterprise.com',
        industry: 'Manufacturing',
        size: 'Large',
        type: 'Prospect',
        status: 'Active',
        revenue: 50000000,
        employees: 500,
        location: 'Chicago, IL',
        description: 'Global manufacturing company with operations across North America and Europe.',
        contacts: ['C002'],
        deals: ['D002'],
        owner: 'Sarah Wilson',
        createdDate: '2025-01-18',
        lastContactDate: '2025-01-21',
        tags: ['Manufacturing', 'Global', 'Large Enterprise'],
        customProperties: {
          'Website': 'https://globalenterprise.com',
          'Founded': '1995',
          'Public/Private': 'Public'
        }
      }
    ];

    const sampleEmailTemplates: EmailTemplate[] = [
      {
        id: 'ET001',
        name: 'Initial Contact Follow-up',
        subject: 'Great meeting you at {{event_name}}',
        content: `Hi {{first_name}},

It was great meeting you at {{event_name}} yesterday. I really enjoyed our conversation about {{topic}}.

As promised, I'm following up with some information about how we've helped companies like {{company_name}} {{specific_benefit}}.

Would you be available for a brief 15-minute call next week to discuss your {{pain_point}} challenges?

Best regards,
{{sender_name}}`,
        type: 'Follow-up',
        category: 'Sales',
        isActive: true,
        createdBy: 'John Smith',
        createdDate: '2025-01-15',
        variables: ['first_name', 'event_name', 'topic', 'company_name', 'specific_benefit', 'pain_point', 'sender_name']
      },
      {
        id: 'ET002',
        name: 'Proposal Follow-up',
        subject: 'Following up on your {{solution_type}} proposal',
        content: `Hi {{first_name}},

I wanted to follow up on the proposal I sent last week for {{solution_type}}.

The proposal includes:
- {{feature_1}}
- {{feature_2}}
- {{feature_3}}

I'd love to answer any questions you might have and discuss next steps.

Are you available for a quick call this week?

Best,
{{sender_name}}`,
        type: 'Follow-up',
        category: 'Sales',
        isActive: true,
        createdBy: 'Sarah Wilson',
        createdDate: '2025-01-18',
        variables: ['first_name', 'solution_type', 'feature_1', 'feature_2', 'feature_3', 'sender_name']
      }
    ];

    setContacts(sampleContacts);
    setDeals(sampleDeals);
    setTasks(sampleTasks);
    setCompanies(sampleCompanies);
    setEmailTemplates(sampleEmailTemplates);
    setActivities([]);
  }, [mounted]);

  // Filter functions
  const filteredContacts = contacts.filter(contact => {
    const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'All' || contact.type === typeFilter;
    const matchesStatus = statusFilter === 'All' || contact.status === statusFilter;
    const matchesAssigned = assignedFilter === 'All' || contact.assignedTo === assignedFilter;
    
    return matchesSearch && matchesType && matchesStatus && matchesAssigned;
  });

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
      case 'Hot':
      case 'Completed':
      case 'Closed Won':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Cold':
      case 'Inactive':
      case 'Cancelled':
      case 'Closed Lost':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Converted':
      case 'Customer':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Pending':
      case 'Open':
      case 'In Progress':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Scheduled':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'High':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Appointment Scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Qualified to Buy':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Presentation Scheduled':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'Decision Maker Bought-In':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Contract Sent':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Closed Won':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Closed Lost':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getLifecycleColor = (lifecycle: string) => {
    switch (lifecycle) {
      case 'Subscriber':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Lead':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Marketing Qualified Lead':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Sales Qualified Lead':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'Opportunity':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Customer':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Evangelist':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Lead':
        return 'ri-user-add-line';
      case 'Customer':
        return 'ri-user-heart-line';
      case 'Prospect':
        return 'ri-user-search-line';
      default:
        return 'ri-user-line';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'Call':
        return 'ri-phone-line';
      case 'Email':
        return 'ri-mail-line';
      case 'Meeting':
        return 'ri-calendar-event-line';
      case 'Note':
        return 'ri-file-text-line';
      case 'Task':
        return 'ri-task-line';
      case 'Quote':
        return 'ri-price-tag-3-line';
      case 'Order':
        return 'ri-shopping-cart-line';
      default:
        return 'ri-information-line';
    }
  };

  // Stats calculations
  const totalContacts = contacts.length;
  const activeDeals = deals.filter(d => !['Closed Won', 'Closed Lost'].includes(d.stage)).length;
  const totalPipelineValue = deals.filter(d => !['Closed Won', 'Closed Lost'].includes(d.stage))
    .reduce((sum, d) => sum + d.value, 0);
  const openTasks = tasks.filter(t => t.status === 'Open').length;
  const overdueT = tasks.filter(t => t.status === 'Open' && new Date(t.dueDate) < new Date()).length;

  // Deal pipeline stats
  const dealsByStage = deals.reduce((acc, deal) => {
    acc[deal.stage] = (acc[deal.stage] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const handleAddContact = () => {
    if (!newContact.name || !newContact.email) return;

    const contact: Contact = {
      id: `C${String(contacts.length + 1).padStart(3, '0')}`,
      name: newContact.name,
      email: newContact.email,
      phone: newContact.phone || '',
      company: newContact.company || '',
      position: newContact.position || '',
      type: newContact.type || 'Lead',
      status: newContact.status || 'Active',
      source: newContact.source || 'Website',
      assignedTo: newContact.assignedTo || 'John Smith',
      lastContact: new Date().toISOString().split('T')[0],
      nextFollowUp: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      notes: newContact.notes || '',
      tags: newContact.tags || [],
      value: newContact.value || 0,
      createdDate: new Date().toISOString().split('T')[0],
      activities: [],
      customProperties: newContact.customProperties || {},
      lifecycle: newContact.lifecycle || 'Lead',
      leadScore: newContact.leadScore || 0,
      socialProfiles: newContact.socialProfiles || {}
    };

    setContacts(prev => [contact, ...prev]);
    setShowAddContactModal(false);
    setNewContact({
      name: '',
      email: '',
      phone: '',
      company: '',
      position: '',
      type: 'Lead',
      status: 'Active',
      source: 'Website',
      assignedTo: 'John Smith',
      notes: '',
      tags: [],
      value: 0,
      lifecycle: 'Lead',
      leadScore: 0,
      customProperties: {},
      socialProfiles: {}
    });
  };

  const handleAddDeal = () => {
    if (!selectedContact || !newDeal.title || !newDeal.value) return;

    const deal: Deal = {
      id: `D${String(deals.length + 1).padStart(3, '0')}`,
      contactId: selectedContact.id,
      title: newDeal.title,
      value: newDeal.value || 0,
      stage: newDeal.stage || 'Appointment Scheduled',
      probability: newDeal.probability || 10,
      expectedCloseDate: newDeal.expectedCloseDate || '',
      assignedTo: newDeal.assignedTo || 'John Smith',
      createdDate: new Date().toISOString().split('T')[0],
      notes: newDeal.notes || '',
      dealType: newDeal.dealType || 'New Business',
      products: newDeal.products || [],
      priority: newDeal.priority || 'Medium',
      lastActivity: new Date().toISOString().split('T')[0],
      daysInStage: 0
    };

    setDeals(prev => [deal, ...prev]);
    setShowAddDealModal(false);
    setNewDeal({
      title: '',
      value: 0,
      stage: 'Appointment Scheduled',
      probability: 10,
      expectedCloseDate: '',
      assignedTo: 'John Smith',
      notes: '',
      dealType: 'New Business',
      products: [],
      priority: 'Medium'
    });
  };

  const handleAddActivity = () => {
    if (!selectedContact || !newActivity.title) return;

    const activity: Activity = {
      id: `A${String(activities.length + 1).padStart(3, '0')}`,
      contactId: selectedContact.id,
      type: newActivity.type || 'Call',
      title: newActivity.title,
      description: newActivity.description || '',
      date: newActivity.date || new Date().toISOString().split('T')[0],
      status: newActivity.status || 'Completed',
      createdBy: newActivity.createdBy || 'John Smith',
      createdDate: new Date().toISOString().split('T')[0],
      priority: newActivity.priority || 'Medium'
    };

    setActivities(prev => [activity, ...prev]);
    setShowAddActivityModal(false);
    setNewActivity({
      title: '',
      description: '',
      type: 'Call',
      priority: 'Medium',
      status: 'Completed',
      date: new Date().toISOString().split('T')[0],
      createdBy: 'John Smith'
    });
  };

  // Don't render until mounted to prevent hydration errors
  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main className={`transition-all duration-300 p-6 ${
        navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')
      }`}>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">CRM Hub</h1>
                <p className="text-gray-600">Manage your entire sales pipeline and customer relationships</p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowImportModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-upload-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Import</span>
                </button>
                <button
                  onClick={() => setShowSequenceModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-mail-send-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Create Sequence</span>
                </button>
                <button
                  onClick={() => setShowAddContactModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-lg hover:bg-orange-700 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Add Contact</span>
                </button>
              </div>
            </div>
          </div>

          {/* Navigation Tabs - Added Pipelines tab */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="flex space-x-8">
              {[
                { id: 'dashboard', name: 'Dashboard', icon: 'ri-dashboard-line', count: null },
                { id: 'contacts', name: 'Contacts', icon: 'ri-user-line', count: totalContacts },
                { id: 'companies', name: 'Companies', icon: 'ri-building-line', count: companies.length },
                { id: 'deals', name: 'Deals', icon: 'ri-handshake-line', count: activeDeals },
                { id: 'pipelines', name: 'Pipelines', icon: 'ri-git-branch-line', count: null },
                { id: 'tasks', name: 'Tasks', icon: 'ri-task-line', count: openTasks },
                { id: 'activities', name: 'Activities', icon: 'ri-calendar-event-line', count: null },
                { id: 'reports', name: 'Reports', icon: 'ri-bar-chart-line', count: null },
                { id: 'automation', name: 'Automation', icon: 'ri-settings-3-line', count: null }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'text-orange-600 border-orange-600'
                      : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <i className={`${tab.icon} w-4 h-4 flex items-center justify-center`}></i>
                  <span className="font-medium">{tab.name}</span>
                  {tab.count !== null && (
                    <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Dashboard View */}
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Total Contacts</p>
                      <p className="text-2xl font-bold text-orange-600">{totalContacts}</p>
                      <p className="text-xs text-green-600 mt-1">↑ 12% from last month</p>
                    </div>
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <i className="ri-user-line w-6 h-6 flex items-center justify-center text-orange-600"></i>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Active Deals</p>
                      <p className="text-2xl font-bold text-blue-600">{activeDeals}</p>
                      <p className="text-xs text-green-600 mt-1">↑ 8% from last month</p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <i className="ri-handshake-line w-6 h-6 flex items-center justify-center text-blue-600"></i>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Pipeline Value</p>
                      <p className="text-2xl font-bold text-green-600">${totalPipelineValue.toLocaleString()}</p>
                      <p className="text-xs text-green-600 mt-1">↑ 23% from last month</p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <i className="ri-money-dollar-circle-line w-6 h-6 flex items-center justify-center text-green-600"></i>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Open Tasks</p>
                      <p className="text-2xl font-bold text-purple-600">{openTasks}</p>
                      {overdueT > 0 && (
                        <p className="text-xs text-red-600 mt-1">{overdueT} overdue</p>
                      )}
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <i className="ri-task-line w-6 h-6 flex items-center justify-center text-purple-600"></i>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pipeline Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Deal Pipeline</h3>
                  <div className="space-y-4">
                    {Object.entries(dealsByStage).map(([stage, count]) => (
                      <div key={stage} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${getStageColor(stage).split(' ')[0]}`}></div>
                          <span className="text-sm text-gray-700">{stage}</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i className="ri-phone-line w-4 h-4 flex items-center justify-center text-green-600"></i>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">Called Alice Johnson</p>
                        <p className="text-xs text-gray-500">2 hours ago by John Smith</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i className="ri-mail-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">Sent proposal to Michael Chen</p>
                        <p className="text-xs text-gray-500">4 hours ago by Sarah Wilson</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i className="ri-calendar-event-line w-4 h-4 flex items-center justify-center text-purple-600"></i>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">Meeting scheduled with David Thompson</p>
                        <p className="text-xs text-gray-500">Yesterday by Lisa Chen</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Pipelines View - NEW SECTION */}
          {activeTab === 'pipelines' && (
            <div className="space-y-6">
              {/* Pipeline Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Sales Pipeline</h2>
                  <p className="text-gray-600">Track deals through your sales process</p>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setShowAddDealModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-lg hover:bg-orange-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Add Deal</span>
                  </button>
                </div>
              </div>

              {/* Pipeline Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Total Pipeline Value</p>
                      <p className="text-2xl font-bold text-green-600">${totalPipelineValue.toLocaleString()}</p>
                      <p className="text-xs text-green-600 mt-1">Active deals only</p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <i className="ri-money-dollar-circle-line w-6 h-6 flex items-center justify-center text-green-600"></i>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Active Deals</p>
                      <p className="text-2xl font-bold text-blue-600">{activeDeals}</p>
                      <p className="text-xs text-blue-600 mt-1">In progress</p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <i className="ri-handshake-line w-6 h-6 flex items-center justify-center text-blue-600"></i>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Avg Deal Size</p>
                      <p className="text-2xl font-bold text-purple-600">
                        ${activeDeals > 0 ? Math.round(totalPipelineValue / activeDeals).toLocaleString() : '0'}
                      </p>
                      <p className="text-xs text-purple-600 mt-1">Current pipeline</p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <i className="ri-price-tag-3-line w-6 h-6 flex items-center justify-center text-purple-600"></i>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-600">Win Rate</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {deals.length > 0 ? Math.round((deals.filter(d => d.stage === 'Closed Won').length / deals.length) * 100) : 0}%
                      </p>
                      <p className="text-xs text-orange-600 mt-1">All time</p>
                    </div>
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <i className="ri-trophy-line w-6 h-6 flex items-center justify-center text-orange-600"></i>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pipeline Board */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 min-h-[600px]">
                  {/* Appointment Scheduled */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        Appointment Scheduled
                      </h3>
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Appointment Scheduled').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Appointment Scheduled').map(deal => (
                        <div key={deal.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-green-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-gray-500">{deal.probability}%</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-calendar-line w-3 h-3 flex items-center justify-center mr-1"></i>
                            <span>{new Date(deal.expectedCloseDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Qualified to Buy */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                        Qualified to Buy
                      </h3>
                      <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Qualified to Buy').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Qualified to Buy').map(deal => (
                        <div key={deal.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-green-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-gray-500">{deal.probability}%</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-calendar-line w-3 h-3 flex items-center justify-center mr-1"></i>
                            <span>{new Date(deal.expectedCloseDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Presentation Scheduled */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-indigo-500 rounded-full mr-2"></div>
                        Presentation Scheduled
                      </h3>
                      <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Presentation Scheduled').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Presentation Scheduled').map(deal => (
                        <div key={deal.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-green-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-gray-500">{deal.probability}%</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-calendar-line w-3 h-3 flex items-center justify-center mr-1"></i>
                            <span>{new Date(deal.expectedCloseDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Decision Maker Bought-In */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                        Decision Maker Bought-In
                      </h3>
                      <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Decision Maker Bought-In').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Decision Maker Bought-In').map(deal => (
                        <div key={deal.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-green-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-gray-500">{deal.probability}%</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-calendar-line w-3 h-3 flex items-center justify-center mr-1"></i>
                            <span>{new Date(deal.expectedCloseDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Contract Sent */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                        Contract Sent
                      </h3>
                      <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Contract Sent').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Contract Sent').map(deal => (
                        <div key={deal.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-green-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-gray-500">{deal.probability}%</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-calendar-line w-3 h-3 flex items-center justify-center mr-1"></i>
                            <span>{new Date(deal.expectedCloseDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Closed Won */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        Closed Won
                      </h3>
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Closed Won').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Closed Won').slice(0, 5).map(deal => (
                        <div key={deal.id} className="bg-white border border-green-200 rounded-lg p-4 shadow-sm">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-green-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-green-600 font-medium">Won</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-check-line w-3 h-3 flex items-center justify-center mr-1 text-green-500"></i>
                            <span>Closed</span>
                          </div>
                        </div>
                      ))}
                      {deals.filter(d => d.stage === 'Closed Won').length > 5 && (
                        <div className="text-center py-2">
                          <span className="text-xs text-gray-500">+{deals.filter(d => d.stage === 'Closed Won').length - 5} more</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Closed Lost */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-semibold text-gray-800 flex items-center">
                        <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                        Closed Lost
                      </h3>
                      <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                        {deals.filter(d => d.stage === 'Closed Lost').length}
                      </span>
                    </div>
                    <div className="space-y-3">
                      {deals.filter(d => d.stage === 'Closed Lost').slice(0, 3).map(deal => (
                        <div key={deal.id} className="bg-white border border-red-200 rounded-lg p-4 shadow-sm">
                          <h4 className="text-sm font-semibold text-gray-900 mb-2">{deal.title}</h4>
                          <div className="text-xs text-gray-500 mb-2">
                            {contacts.find(c => c.id === deal.contactId)?.name || 'Unknown Contact'}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-600">${deal.value.toLocaleString()}</span>
                            <span className="text-xs text-red-600 font-medium">Lost</span>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <i className="ri-close-line w-3 h-3 flex items-center justify-center mr-1 text-red-500"></i>
                            <span>Closed</span>
                          </div>
                        </div>
                      ))}
                      {deals.filter(d => d.stage === 'Closed Lost').length > 3 && (
                        <div className="text-center py-2">
                          <span className="text-xs text-gray-500">+{deals.filter(d => d.stage === 'Closed Lost').length - 3} more</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Filters */}
          {activeTab === 'contacts' && (
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <i className="ri-search-line w-5 h-5 flex items-center justify-center absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input
                      type="text"
                      placeholder="Search contacts, companies, or deals..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-80"
                    />
                  </div>

                  {/* View Type Selector */}
                  <div className="flex items-center bg-white rounded-lg border border-gray-300 p-1">
                    <button
                      onClick={() => setViewType('table')}
                      className={`px-3 py-1 text-sm font-medium rounded cursor-pointer whitespace-nowrap ${
                        viewType === 'table' 
                          ? 'bg-orange-600 text-white' 
                          : 'text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      <i className="ri-table-line w-4 h-4 flex items-center justify-center mr-1 inline-block"></i>
                      Table
                    </button>
                    <button
                      onClick={() => setViewType('kanban')}
                      className={`px-3 py-1 text-sm font-medium rounded cursor-pointer whitespace-nowrap ${
                        viewType === 'kanban' 
                          ? 'bg-orange-600 text-white' 
                          : 'text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      <i className="ri-layout-column-line w-4 h-4 flex items-center justify-center mr-1 inline-block"></i>
                      Kanban
                    </button>
                    <button
                      onClick={() => setViewType('list')}
                      className={`px-3 py-1 text-sm font-medium rounded cursor-pointer whitespace-nowrap ${
                        viewType === 'list' 
                          ? 'bg-orange-600 text-white' 
                          : 'text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      <i className="ri-list-unordered w-4 h-4 flex items-center justify-center mr-1 inline-block"></i>
                      List
                    </button>
                  </div>

                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                  >
                    <option value="All">All Types</option>
                    <option value="Lead">Leads</option>
                    <option value="Customer">Customers</option>
                    <option value="Prospect">Prospects</option>
                  </select>

                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                  >
                    <option value="All">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Hot">Hot</option>
                    <option value="Cold">Cold</option>
                    <option value="Converted">Converted</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                </div>

                <div className="text-sm text-gray-500">
                  {filteredContacts.length} of {contacts.length} contacts
                </div>
              </div>
            </div>
          )}

          {/* Content */}
          <div className="overflow-x-auto">
            {activeTab === 'contacts' && (
              <>
                {/* Kanban View */}
                {viewType === 'kanban' && (
                  <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 min-h-screen">
                      {/* Lead Stage */}
                      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                        <div className="px-4 py-3 bg-blue-50 border-b border-blue-200 rounded-t-lg">
                          <div className="flex items-center justify-between">
                            <h3 className="text-sm font-semibold text-blue-800 flex items-center">
                              <i className="ri-user-add-line w-4 h-4 flex items-center justify-center mr-2"></i>
                              New Leads
                            </h3>
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {filteredContacts.filter(c => c.lifecycle === 'Lead' || c.lifecycle === 'Subscriber').length}
                            </span>
                          </div>
                        </div>
                        <div className="p-4 space-y-3 min-h-96">
                          {filteredContacts
                            .filter(c => c.lifecycle === 'Lead' || c.lifecycle === 'Subscriber')
                            .map(contact => (
                              <div 
                                key={contact.id} 
                                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => setSelectedContact(contact)}
                              >
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                      {contact.avatar ? (
                                        <img src={contact.avatar} alt={contact.name} className="w-8 h-8 rounded-full object-cover" />
                                      ) : (
                                        <span className="text-xs font-semibold text-blue-600">
                                          {contact.name.charAt(0)}
                                        </span>
                                      )}
                                    </div>
                                    <div>
                                      <h4 className="text-sm font-semibold text-gray-900">{contact.name}</h4>
                                      <p className="text-xs text-gray-500">{contact.company}</p>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-xs font-medium text-green-600">
                                      ${contact.value.toLocaleString()}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      Score: {contact.leadScore}
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(contact.status)}`}>
                                    {contact.status}
                                  </span>
                                  <div className="flex items-center space-x-1">
                                    {contact.socialProfiles?.linkedin && (
                                      <i className="ri-linkedin-fill w-3 h-3 flex items-center justify-center text-blue-600"></i>
                                    )}
                                    {contact.socialProfiles?.twitter && (
                                      <i className="ri-twitter-fill w-3 h-3 flex items-center justify-center text-blue-400"></i>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="mt-3 text-xs text-gray-500">
                                  <div>Source: {contact.source}</div>
                                  <div>Next: {new Date(contact.nextFollowUp).toLocaleDateString()}</div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>

                      {/* Marketing Qualified Lead Stage */}
                      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                        <div className="px-4 py-3 bg-purple-50 border-b border-purple-200 rounded-t-lg">
                          <div className="flex items-center justify-between">
                            <h3 className="text-sm font-semibold text-purple-800 flex items-center">
                              <i className="ri-user-star-line w-4 h-4 flex items-center justify-center mr-2"></i>
                              Marketing Qualified
                            </h3>
                            <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                              {filteredContacts.filter(c => c.lifecycle === 'Marketing Qualified Lead').length}
                            </span>
                          </div>
                        </div>
                        <div className="p-4 space-y-3 min-h-96">
                          {filteredContacts
                            .filter(c => c.lifecycle === 'Marketing Qualified Lead')
                            .map(contact => (
                              <div 
                                key={contact.id} 
                                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => setSelectedContact(contact)}
                              >
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                                      {contact.avatar ? (
                                        <img src={contact.avatar} alt={contact.name} className="w-8 h-8 rounded-full object-cover" />
                                      ) : (
                                        <span className="text-xs font-semibold text-purple-600">
                                          {contact.name.charAt(0)}
                                        </span>
                                      )}
                                    </div>
                                    <div>
                                      <h4 className="text-sm font-semibold text-gray-900">{contact.name}</h4>
                                      <p className="text-xs text-gray-500">{contact.company}</p>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-xs font-medium text-green-600">
                                      ${contact.value.toLocaleString()}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      Score: {contact.leadScore}
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(contact.status)}`}>
                                    {contact.status}
                                  </span>
                                  <div className="flex items-center space-x-1">
                                    {contact.socialProfiles?.linkedin && (
                                      <i className="ri-linkedin-fill w-3 h-3 flex items-center justify-center text-blue-600"></i>
                                    )}
                                    {contact.socialProfiles?.twitter && (
                                      <i className="ri-twitter-fill w-3 h-3 flex items-center justify-center text-blue-400"></i>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="mt-3 text-xs text-gray-500">
                                  <div>Source: {contact.source}</div>
                                  <div>Next: {new Date(contact.nextFollowUp).toLocaleDateString()}</div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>

                      {/* Sales Qualified Lead Stage */}
                      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                        <div className="px-4 py-3 bg-orange-50 border-b border-orange-200 rounded-t-lg">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-semibold text-orange-800 flex items-center">
                              <i className="ri-user-settings-line w-4 h-4 flex items-center justify-center mr-2"></i>
                              Sales Qualified
                            </h3>
                            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                              {filteredContacts.filter(c => c.lifecycle === 'Sales Qualified Lead' || c.lifecycle === 'Opportunity').length}
                            </span>
                          </div>
                        </div>
                        <div className="p-4 space-y-3 min-h-96">
                          {filteredContacts
                            .filter(c => c.lifecycle === 'Sales Qualified Lead' || c.lifecycle === 'Opportunity')
                            .map(contact => (
                              <div 
                                key={contact.id} 
                                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => setSelectedContact(contact)}
                              >
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center">
                                      {contact.avatar ? (
                                        <img src={contact.avatar} alt={contact.name} className="w-8 h-8 rounded-full object-cover" />
                                      ) : (
                                        <span className="text-xs font-semibold text-orange-600">
                                          {contact.name.charAt(0)}
                                        </span>
                                      )}
                                    </div>
                                    <div>
                                      <h4 className="text-sm font-semibold text-gray-900">{contact.name}</h4>
                                      <p className="text-xs text-gray-500">{contact.company}</p>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-xs font-medium text-green-600">
                                      ${contact.value.toLocaleString()}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      Score: {contact.leadScore}
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(contact.status)}`}>
                                    {contact.status}
                                  </span>
                                  <div className="flex items-center space-x-1">
                                    {contact.socialProfiles?.linkedin && (
                                      <i className="ri-linkedin-fill w-3 h-3 flex items-center justify-center text-blue-600"></i>
                                    )}
                                    {contact.socialProfiles?.twitter && (
                                      <i className="ri-twitter-fill w-3 h-3 flex items-center justify-center text-blue-400"></i>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="mt-3 text-xs text-gray-500">
                                  <div>Source: {contact.source}</div>
                                  <div>Next: {new Date(contact.nextFollowUp).toLocaleDateString()}</div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>

                      {/* Customer Stage */}
                      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                        <div className="px-4 py-3 bg-green-50 border-b border-green-200 rounded-t-lg">
                          <div className="flex items-center justify-between">
                            <h3 className="text-sm font-semibold text-green-800 flex items-center">
                              <i className="ri-user-heart-line w-4 h-4 flex items-center justify-center mr-2"></i>
                              Customers
                            </h3>
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              {filteredContacts.filter(c => c.lifecycle === 'Customer' || c.lifecycle === 'Evangelist').length}
                            </span>
                          </div>
                        </div>
                        <div className="p-4 space-y-3 min-h-96">
                          {filteredContacts
                            .filter(c => c.lifecycle === 'Customer' || c.lifecycle === 'Evangelist')
                            .map(contact => (
                              <div 
                                key={contact.id} 
                                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => setSelectedContact(contact)}
                              >
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                      {contact.avatar ? (
                                        <img src={contact.avatar} alt={contact.name} className="w-8 h-8 rounded-full object-cover" />
                                      ) : (
                                        <span className="text-xs font-semibold text-green-600">
                                          {contact.name.charAt(0)}
                                        </span>
                                      )}
                                    </div>
                                    <div>
                                      <h4 className="text-sm font-semibold text-gray-900">{contact.name}</h4>
                                      <p className="text-xs text-gray-500">{contact.company}</p>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <div className="text-xs font-medium text-green-600">
                                      ${contact.value.toLocaleString()}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      Score: {contact.leadScore}
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(contact.status)}`}>
                                    {contact.status}
                                  </span>
                                  {contact.lifecycle === 'Evangelist' && (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                      <i className="ri-star-fill w-3 h-3 flex items-center justify-center mr-1"></i>
                                      Evangelist
                                    </span>
                                  )}
                                  <div className="flex items-center space-x-1">
                                    {contact.socialProfiles?.linkedin && (
                                      <i className="ri-linkedin-fill w-3 h-3 flex items-center justify-center text-blue-600"></i>
                                    )}
                                    {contact.socialProfiles?.twitter && (
                                      <i className="ri-twitter-fill w-3 h-3 flex items-center justify-center text-blue-400"></i>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="mt-3 text-xs text-gray-500">
                                  <div>Source: {contact.source}</div>
                                  <div>Last Contact: {new Date(contact.lastContact).toLocaleDateString()}</div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Table View */}
                {viewType === 'table' && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stage</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Follow-up</th>
                        <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredContacts.map(contact => (
                        <tr key={contact.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => setSelectedContact(contact)}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                {contact.avatar ? (
                                  <img src={contact.avatar} alt={contact.name} className="w-10 h-10 rounded-full object-cover" />
                                ) : (
                                  <span className="text-sm font-semibold text-gray-600">
                                    {contact.name.charAt(0)}
                                  </span>
                                )}
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">{contact.name}</div>
                                <div className="text-sm text-gray-500">{contact.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{contact.company}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLifecycleColor(contact.lifecycle)}`}>
                              {contact.lifecycle}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(contact.status)}`}>
                              {contact.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                            ${contact.value.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{contact.assignedTo}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(contact.nextFollowUp).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <div className="flex items-center justify-center space-x-2">
                              <button className="text-gray-600 hover:text-gray-900">
                                <i className="ri-edit-line w-4 h-4 flex items-center justify-center"></i>
                              </button>
                              <button className="text-gray-600 hover:text-gray-900">
                                <i className="ri-delete-bin-line w-4 h-4 flex items-center justify-center"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
