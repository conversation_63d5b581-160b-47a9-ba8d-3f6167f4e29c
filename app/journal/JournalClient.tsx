
'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Link from 'next/link';

interface JournalLine {
  id: string;
  account: string;
  description: string;
  costCenter: string;
  debit: number;
  credit: number;
}

interface JournalEntry {
  id: string;
  date: string;
  journalType: string;
  reference: string;
  description: string;
  status: 'draft' | 'posted';
  lines: JournalLine[];
  totalDebit: number;
  totalCredit: number;
  attachments: string[];
  createdAt: string;
  lastModified: string;
}

const journalTypes = [
  'General Journal',
  'Sales Journal', 
  'Purchase Journal',
  'Cash Receipts',
  'Cash Disbursements',
  'Adjustment',
  'Closing Entries'
];

const chartOfAccounts = [
  '1000 - Cash',
  '1100 - Accounts Receivable',
  '1200 - Inventory',
  '1500 - Equipment',
  '2000 - Accounts Payable',
  '2100 - Accrued Expenses',
  '3000 - Owner\'s Equity',
  '4000 - Sales Revenue',
  '5000 - Cost of Goods Sold',
  '6000 - Operating Expenses',
  '6100 - Rent Expense',
  '6200 - Utilities Expense',
  '6300 - Marketing Expense'
];

const costCenters = [
  'CC001 - Administration',
  'CC002 - Sales',
  'CC003 - Production',
  'CC004 - Marketing',
  'CC005 - IT'
];

export default function JournalClient() {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<JournalEntry[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    journalType: '',
    account: '',
    status: ''
  });

  useEffect(() => {
    // Mock data
    const mockEntries: JournalEntry[] = [
      {
        id: 'JE001',
        date: '2024-01-15',
        journalType: 'Sales Journal',
        reference: 'INV-2024-001',
        description: 'Sale to Customer ABC',
        status: 'posted',
        lines: [
          {
            id: '1',
            account: '1100 - Accounts Receivable',
            description: 'Sale to Customer ABC',
            costCenter: 'CC002 - Sales',
            debit: 1200,
            credit: 0
          },
          {
            id: '2', 
            account: '4000 - Sales Revenue',
            description: 'Sale to Customer ABC',
            costCenter: 'CC002 - Sales',
            debit: 0,
            credit: 1200
          }
        ],
        totalDebit: 1200,
        totalCredit: 1200,
        attachments: ['invoice-001.pdf'],
        createdAt: '2024-01-15 09:30',
        lastModified: '2024-01-15 09:35'
      },
      {
        id: 'JE002',
        date: '2024-01-16',
        journalType: 'Purchase Journal',
        reference: 'PO-2024-005',
        description: 'Office supplies purchase',
        status: 'draft',
        lines: [
          {
            id: '1',
            account: '6000 - Operating Expenses',
            description: 'Office supplies',
            costCenter: 'CC001 - Administration',
            debit: 350,
            credit: 0
          },
          {
            id: '2',
            account: '2000 - Accounts Payable',
            description: 'Office supplies payable',
            costCenter: '',
            debit: 0,
            credit: 350
          }
        ],
        totalDebit: 350,
        totalCredit: 350,
        attachments: ['receipt-005.pdf'],
        createdAt: '2024-01-16 14:20',
        lastModified: '2024-01-16 14:25'
      },
      {
        id: 'JE003',
        date: '2024-01-17',
        journalType: 'Cash Receipts',
        reference: 'PAY-001',
        description: 'Payment received from Customer ABC',
        status: 'posted',
        lines: [
          {
            id: '1',
            account: '1000 - Cash',
            description: 'Payment received',
            costCenter: '',
            debit: 1200,
            credit: 0
          },
          {
            id: '2',
            account: '1100 - Accounts Receivable',
            description: 'Payment on invoice INV-2024-001',
            costCenter: 'CC002 - Sales',
            debit: 0,
            credit: 1200
          }
        ],
        totalDebit: 1200,
        totalCredit: 1200,
        attachments: [],
        createdAt: '2024-01-17 11:15',
        lastModified: '2024-01-17 11:15'
      }
    ];
    
    setEntries(mockEntries);
    setFilteredEntries(mockEntries);
  }, []);

  useEffect(() => {
    let filtered = entries;

    if (filters.dateFrom) {
      filtered = filtered.filter(entry => entry.date >= filters.dateFrom);
    }
    if (filters.dateTo) {
      filtered = filtered.filter(entry => entry.date <= filters.dateTo);
    }
    if (filters.journalType) {
      filtered = filtered.filter(entry => entry.journalType === filters.journalType);
    }
    if (filters.account) {
      filtered = filtered.filter(entry => 
        entry.lines.some(line => line.account.includes(filters.account))
      );
    }
    if (filters.status) {
      filtered = filtered.filter(entry => entry.status === filters.status);
    }

    setFilteredEntries(filtered);
  }, [filters, entries]);

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const clearFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      journalType: '',
      account: '',
      status: ''
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'px-3 py-1 rounded-full text-xs font-medium';
    if (status === 'posted') {
      return `${baseClasses} bg-green-100 text-green-800`;
    }
    return `${baseClasses} bg-yellow-100 text-yellow-800`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Journal Entries</h1>
              <p className="text-gray-600">Manage accounting journal entries</p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
              >
                <i className="ri-filter-line"></i>
                Filters
              </button>
              <Link
                href="/journal/new"
                className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
              >
                <i className="ri-add-line"></i>
                New Entry
              </Link>
            </div>
          </div>

          {showFilters && (
            <div className="bg-white p-4 rounded-lg border mb-6">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date From
                  </label>
                  <input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date To
                  </label>
                  <input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Journal Type
                  </label>
                  <select
                    value={filters.journalType}
                    onChange={(e) => handleFilterChange('journalType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                  >
                    <option value="">All Types</option>
                    {journalTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account
                  </label>
                  <input
                    type="text"
                    placeholder="Search account..."
                    value={filters.account}
                    onChange={(e) => handleFilterChange('account', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                  >
                    <option value="">All Status</option>
                    <option value="draft">Draft</option>
                    <option value="posted">Posted</option>
                  </select>
                </div>
              </div>
              <div className="mt-4 flex justify-end">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 cursor-pointer whitespace-nowrap"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entry ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reference
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEntries.map((entry) => (
                  <tr key={entry.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm font-medium text-blue-600">
                      <Link href={`/journal/${entry.id}`} className="hover:underline cursor-pointer">
                        {entry.id}
                      </Link>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {new Date(entry.date).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {entry.journalType}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {entry.reference}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {entry.description}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 text-right font-medium">
                      {formatCurrency(entry.totalDebit)}
                    </td>
                    <td className="px-4 py-3">
                      <span className={getStatusBadge(entry.status)}>
                        {entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Link
                          href={`/journal/${entry.id}`}
                          className="text-blue-600 hover:text-blue-800 cursor-pointer"
                          title="View Details"
                        >
                          <div className="w-8 h-8 flex items-center justify-center">
                            <i className="ri-eye-line"></i>
                          </div>
                        </Link>
                        {entry.status === 'draft' && (
                          <Link
                            href={`/journal/${entry.id}/edit`}
                            className="text-green-600 hover:text-green-800 cursor-pointer"
                            title="Edit Entry"
                          >
                            <div className="w-8 h-8 flex items-center justify-center">
                              <i className="ri-edit-line"></i>
                            </div>
                          </Link>
                        )}
                        {entry.attachments.length > 0 && (
                          <button
                            className="text-gray-600 hover:text-gray-800 cursor-pointer"
                            title="View Attachments"
                          >
                            <div className="w-8 h-8 flex items-center justify-center">
                              <i className="ri-attachment-line"></i>
                            </div>
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredEntries.length === 0 && (
            <div className="text-center py-12">
              <i className="ri-file-list-line text-4xl text-gray-400 mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No journal entries found</h3>
              <p className="text-gray-500 mb-4">
                {entries.length === 0 
                  ? "Get started by creating your first journal entry."
                  : "Try adjusting your filters to see more results."
                }
              </p>
              <Link
                href="/journal/new"
                className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
              >
                <i className="ri-add-line"></i>
                New Journal Entry
              </Link>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-between items-center text-sm text-gray-600">
          <div>
            Showing {filteredEntries.length} of {entries.length} entries
          </div>
          <div className="flex gap-4">
            <Link
              href="/journal/reports"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 cursor-pointer whitespace-nowrap"
            >
              <i className="ri-bar-chart-line"></i>
              View Reports
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
