
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Link from 'next/link';

interface JournalLine {
  id: string;
  account: string;
  description: string;
  costCenter: string;
  debit: number;
  credit: number;
}

interface JournalEntry {
  id: string;
  date: string;
  journalType: string;
  reference: string;
  description: string;
  status: 'draft' | 'posted';
  lines: JournalLine[];
  totalDebit: number;
  totalCredit: number;
  attachments: string[];
  createdAt: string;
  lastModified: string;
  createdBy: string;
  approvedBy?: string;
  approvedAt?: string;
}

interface JournalDetailClientProps {
  entryId: string;
}

export default function JournalDetailClient({ entryId }: JournalDetailClientProps) {
  const router = useRouter();
  const [entry, setEntry] = useState<JournalEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPostModal, setShowPostModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);

  useEffect(() => {
    // Mock data fetch
    const mockEntry: JournalEntry = {
      id: entryId,
      date: '2024-01-15',
      journalType: 'Sales Journal',
      reference: 'INV-2024-001',
      description: 'Sale to Customer ABC - Monthly service contract',
      status: entryId === 'JE002' ? 'draft' : 'posted',
      lines: [
        {
          id: '1',
          account: '1100 - Accounts Receivable',
          description: 'Sale to Customer ABC - Monthly service contract',
          costCenter: 'CC002 - Sales',
          debit: 1200,
          credit: 0
        },
        {
          id: '2', 
          account: '4000 - Sales Revenue',
          description: 'Monthly service contract revenue',
          costCenter: 'CC002 - Sales',
          debit: 0,
          credit: 1000
        },
        {
          id: '3',
          account: '2300 - Sales Tax Payable',
          description: 'Sales tax on service contract',
          costCenter: '',
          debit: 0,
          credit: 200
        }
      ],
      totalDebit: 1200,
      totalCredit: 1200,
      attachments: ['invoice-001.pdf', 'contract-abc.pdf'],
      createdAt: '2024-01-15 09:30:00',
      lastModified: '2024-01-15 09:35:00',
      createdBy: 'John Smith',
      approvedBy: entryId !== 'JE002' ? 'Jane Doe' : undefined,
      approvedAt: entryId !== 'JE002' ? '2024-01-15 10:15:00' : undefined
    };

    setTimeout(() => {
      setEntry(mockEntry);
      setLoading(false);
    }, 500);
  }, [entryId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString();
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = 'px-3 py-1 rounded-full text-sm font-medium';
    if (status === 'posted') {
      return `${baseClasses} bg-green-100 text-green-800`;
    }
    return `${baseClasses} bg-yellow-100 text-yellow-800`;
  };

  const handlePostEntry = () => {
    if (entry) {
      setEntry(prev => prev ? {
        ...prev,
        status: 'posted',
        approvedBy: 'Current User',
        approvedAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      } : null);
      setShowPostModal(false);
    }
  };

  const handleCancelEntry = () => {
    // Handle cancel logic
    setShowCancelModal(false);
    router.push('/journal');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="p-6 flex items-center justify-center">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-blue-600 animate-spin mb-4"></i>
            <p className="text-gray-600">Loading journal entry...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!entry) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="p-6 flex items-center justify-center">
          <div className="text-center">
            <i className="ri-file-forbid-line text-4xl text-gray-400 mb-4"></i>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Entry Not Found</h2>
            <p className="text-gray-600 mb-4">The journal entry you're looking for doesn't exist.</p>
            <Link
              href="/journal"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
            >
              <i className="ri-arrow-left-line"></i>
              Back to Journal
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Link
                href="/journal"
                className="text-gray-600 hover:text-gray-800 cursor-pointer"
              >
                <div className="w-8 h-8 flex items-center justify-center">
                  <i className="ri-arrow-left-line text-xl"></i>
                </div>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Journal Entry {entry.id}
                </h1>
                <p className="text-gray-600">{entry.description}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className={getStatusBadge(entry.status)}>
                {entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
              </span>
              {entry.status === 'draft' && (
                <>
                  <Link
                    href={`/journal/${entry.id}/edit`}
                    className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-edit-line"></i>
                    Edit
                  </Link>
                  <button
                    onClick={() => setShowPostModal(true)}
                    className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-check-line"></i>
                    Post Entry
                  </button>
                  <button
                    onClick={() => setShowCancelModal(true)}
                    className="flex items-center gap-2 text-red-600 hover:text-red-800 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-close-line"></i>
                    Cancel
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Entry Details</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Entry ID
                  </label>
                  <p className="text-gray-900 font-medium">{entry.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Date
                  </label>
                  <p className="text-gray-900">{new Date(entry.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Journal Type
                  </label>
                  <p className="text-gray-900">{entry.journalType}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Reference
                  </label>
                  <p className="text-gray-900">{entry.reference || '-'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    Status
                  </label>
                  <span className={getStatusBadge(entry.status)}>
                    {entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
                  </span>
                </div>
              </div>
              
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-500 mb-1">
                  Description
                </label>
                <p className="text-gray-900">{entry.description}</p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Journal Lines</h2>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Account
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Description
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Cost Center
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                        Debit
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                        Credit
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {entry.lines.map((line) => (
                      <tr key={line.id}>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {line.account}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {line.description}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-600">
                          {line.costCenter || '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 text-right font-medium">
                          {line.debit > 0 ? formatCurrency(line.debit) : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 text-right font-medium">
                          {line.credit > 0 ? formatCurrency(line.credit) : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gray-50 border-t">
                    <tr>
                      <td colSpan={3} className="px-4 py-3 text-right font-medium text-gray-900">
                        Totals:
                      </td>
                      <td className="px-4 py-3 text-right font-bold text-gray-900">
                        {formatCurrency(entry.totalDebit)}
                      </td>
                      <td className="px-4 py-3 text-right font-bold text-gray-900">
                        {formatCurrency(entry.totalCredit)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {entry.attachments.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Attachments</h2>
                
                <div className="space-y-3">
                  {entry.attachments.map((attachment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <i className="ri-file-pdf-line text-red-500"></i>
                        <span className="text-sm text-gray-900">{attachment}</span>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 cursor-pointer">
                        <div className="w-8 h-8 flex items-center justify-center">
                          <i className="ri-download-line"></i>
                        </div>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Entry Summary</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Debits:</span>
                  <span className="font-medium">{formatCurrency(entry.totalDebit)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Credits:</span>
                  <span className="font-medium">{formatCurrency(entry.totalCredit)}</span>
                </div>
                <div className="flex justify-between border-t pt-3">
                  <span className="text-gray-600">Balance:</span>
                  <span className="font-medium text-green-600">
                    {entry.totalDebit === entry.totalCredit ? 'Balanced' : 'Unbalanced'}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Audit Trail</h3>
              
              <div className="space-y-4">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <i className="ri-user-line text-gray-500"></i>
                    <span className="text-sm font-medium text-gray-900">Created by</span>
                  </div>
                  <p className="text-sm text-gray-600 ml-6">{entry.createdBy}</p>
                  <p className="text-xs text-gray-500 ml-6">{formatDateTime(entry.createdAt)}</p>
                </div>
                
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <i className="ri-edit-line text-gray-500"></i>
                    <span className="text-sm font-medium text-gray-900">Last modified</span>
                  </div>
                  <p className="text-xs text-gray-500 ml-6">{formatDateTime(entry.lastModified)}</p>
                </div>

                {entry.approvedBy && entry.approvedAt && (
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <i className="ri-check-line text-green-500"></i>
                      <span className="text-sm font-medium text-gray-900">Approved by</span>
                    </div>
                    <p className="text-sm text-gray-600 ml-6">{entry.approvedBy}</p>
                    <p className="text-xs text-gray-500 ml-6">{formatDateTime(entry.approvedAt)}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Quick Actions</h4>
              <div className="space-y-2">
                <Link
                  href="/journal/reports"
                  className="flex items-center gap-2 text-blue-700 hover:text-blue-800 cursor-pointer text-sm whitespace-nowrap"
                >
                  <i className="ri-bar-chart-line"></i>
                  View Reports
                </Link>
                <Link
                  href="/journal"
                  className="flex items-center gap-2 text-blue-700 hover:text-blue-800 cursor-pointer text-sm whitespace-nowrap"
                >
                  <i className="ri-file-list-line"></i>
                  All Entries
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showPostModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Post Journal Entry</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to post this journal entry? Once posted, it cannot be edited.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowPostModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={handlePostEntry}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap"
              >
                Post Entry
              </button>
            </div>
          </div>
        </div>
      )}

      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancel Journal Entry</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to cancel this journal entry? This action cannot be undone.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowCancelModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
              >
                Keep Entry
              </button>
              <button
                onClick={handleCancelEntry}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 cursor-pointer whitespace-nowrap"
              >
                Cancel Entry
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
