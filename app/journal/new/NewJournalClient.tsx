
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Link from 'next/link';

interface JournalLine {
  id: string;
  account: string;
  accountCode: string;
  partner: string;
  label: string;
  description: string;
  costCenter: string;
  debit: number;
  credit: number;
  reference: string;
}

interface JournalEntry {
  id: string;
  date: string;
  journalType: string;
  reference: string;
  description: string;
  lines: JournalLine[];
  attachments: File[];
  status: 'Draft' | 'Posted';
  createdAt: string;
  createdBy: string;
  postedAt?: string;
  postedBy?: string;
}

const journalTypes = [
  'General Journal',
  'Sales Journal',
  'Purchase Journal',
  'Cash Receipts',
  'Cash Disbursements',
  'Adjustment',
  'Closing Entries'
];

const chartOfAccounts = [
  { code: '1000', name: 'Cash', fullName: '1000 - Cash' },
  { code: '1100', name: 'Accounts Receivable', fullName: '1100 - Accounts Receivable' },
  { code: '1200', name: 'Inventory', fullName: '1200 - Inventory' },
  { code: '1300', name: 'Prepaid Expenses', fullName: '1300 - Prepaid Expenses' },
  { code: '1400', name: 'Supplies', fullName: '1400 - Supplies' },
  { code: '1500', name: 'Equipment', fullName: '1500 - Equipment' },
  { code: '1600', name: 'Accumulated Depreciation', fullName: '1600 - Accumulated Depreciation' },
  { code: '2000', name: 'Accounts Payable', fullName: '2000 - Accounts Payable' },
  { code: '2100', name: 'Accrued Expenses', fullName: '2100 - Accrued Expenses' },
  { code: '2200', name: 'Notes Payable', fullName: '2200 - Notes Payable' },
  { code: '2300', name: 'Unearned Revenue', fullName: '2300 - Unearned Revenue' },
  { code: '3000', name: "Owner's Equity", fullName: '3000 - Owner\'s Equity' },
  { code: '3100', name: 'Retained Earnings', fullName: '3100 - Retained Earnings' },
  { code: '4000', name: 'Sales Revenue', fullName: '4000 - Sales Revenue' },
  { code: '4100', name: 'Service Revenue', fullName: '4100 - Service Revenue' },
  { code: '5000', name: 'Cost of Goods Sold', fullName: '5000 - Cost of Goods Sold' },
  { code: '6000', name: 'Operating Expenses', fullName: '6000 - Operating Expenses' },
  { code: '6100', name: 'Rent Expense', fullName: '6100 - Rent Expense' },
  { code: '6200', name: 'Utilities Expense', fullName: '6200 - Utilities Expense' },
  { code: '6300', name: 'Marketing Expense', fullName: '6300 - Marketing Expense' },
  { code: '6400', name: 'Depreciation Expense', fullName: '6400 - Depreciation Expense' },
  { code: '6500', name: 'Interest Expense', fullName: '6500 - Interest Expense' }
];

const costCenters = [
  'CC001 - Administration',
  'CC002 - Sales',
  'CC003 - Production',
  'CC004 - Marketing',
  'CC005 - IT',
  'CC006 - Human Resources',
  'CC007 - Finance'
];

const partners = [
  'ABC Corporation',
  'XYZ Ltd.',
  'Smith & Associates',
  'Global Trading Co.',
  'Tech Solutions Inc.',
  'Metro Services',
  'Prime Suppliers',
  'Elite Partners'
];

export default function NewJournalClient() {
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const [entry, setEntry] = useState<
    Omit<JournalEntry, 'id' | 'status' | 'createdAt' | 'createdBy'>
  >({
    date: new Date().toISOString().split('T')[0],
    journalType: '',
    reference: '',
    description: '',
    lines: [
      {
        id: '1',
        account: '',
        accountCode: '',
        partner: '',
        label: '',
        description: '',
        costCenter: '',
        debit: 0,
        credit: 0,
        reference: ''
      },
      {
        id: '2',
        account: '',
        accountCode: '',
        partner: '',
        label: '',
        description: '',
        costCenter: '',
        debit: 0,
        credit: 0,
        reference: ''
      }
    ],
    attachments: []
  });

  const [totals, setTotals] = useState({
    debit: 0,
    credit: 0,
    isBalanced: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showAccountDropdown, setShowAccountDropdown] = useState<string | null>(null);
  const [showPartnerDropdown, setShowPartnerDropdown] = useState<string | null>(null);
  const [showCostCenterDropdown, setShowCostCenterDropdown] = useState<string | null>(null);

  // Load navigation mode
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) setNavMode(savedNavMode);
  }, []);

  // Calculate totals & balance status
  useEffect(() => {
    const totalDebit = entry.lines.reduce((sum, line) => sum + (line.debit || 0), 0);
    const totalCredit = entry.lines.reduce((sum, line) => sum + (line.credit || 0), 0);
    const isBalanced = totalDebit === totalCredit && totalDebit > 0;

    setTotals({ debit: totalDebit, credit: totalCredit, isBalanced });
  }, [entry.lines]);

  const generateEntryId = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const time = String(Date.now()).slice(-4);
    return `JE${year}${month}${day}${time}`;
  };

  const addLine = () => {
    const newLine: JournalLine = {
      id: String(entry.lines.length + 1),
      account: '',
      accountCode: '',
      partner: '',
      label: '',
      description: '',
      costCenter: '',
      debit: 0,
      credit: 0,
      reference: ''
    };
    setEntry(prev => ({ ...prev, lines: [...prev.lines, newLine] }));
  };

  const removeLine = (lineId: string) => {
    if (entry.lines.length <= 2) return;
    setEntry(prev => ({
      ...prev,
      lines: prev.lines.filter(line => line.id !== lineId)
    }));
  };

  const updateLine = (
    lineId: string,
    field: keyof JournalLine,
    value: string | number
  ) => {
    setEntry(prev => ({
      ...prev,
      lines: prev.lines.map(line => {
        if (line.id !== lineId) return line;
        const updatedLine = { ...line, [field]: value };

        // Auto‑populate account code when an account is selected
        if (field === 'account') {
          const selected = chartOfAccounts.find(acc => acc.fullName === value);
          if (selected) updatedLine.accountCode = selected.code;
        }
        return updatedLine;
      })
    }));
  };

  const updateEntry = (
    field: keyof Omit<
      JournalEntry,
      'id' | 'status' | 'createdAt' | 'createdBy' | 'lines' | 'attachments'
    >,
    value: string
  ) => {
    setEntry(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setEntry(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const removeAttachment = (index: number) => {
    setEntry(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const validateEntry = () => {
    const newErrors: Record<string, string> = {};

    if (!entry.journalType) newErrors.journalType = 'Journal type is required';
    if (!entry.description) newErrors.description = 'Description is required';

    entry.lines.forEach((line, idx) => {
      if (!line.account) newErrors[`line_${idx}_account`] = 'Account is required';
      if (!line.description && !line.label)
        newErrors[`line_${idx}_description`] = 'Description or label is required';
      if (line.debit === 0 && line.credit === 0)
        newErrors[`line_${idx}_amount`] = 'Either debit or credit amount is required';
      if (line.debit > 0 && line.credit > 0)
        newErrors[`line_${idx}_amount`] = 'A line cannot have both debit and credit amounts';
    });

    if (!totals.isBalanced) newErrors.balance = 'Total debits must equal total credits';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const saveJournalEntry = (status: 'Draft' | 'Posted'): JournalEntry => {
    const entryId = generateEntryId();
    const timestamp = new Date().toISOString();

    const journalEntry: JournalEntry = {
      id: entryId,
      date: entry.date,
      journalType: entry.journalType,
      reference: entry.reference,
      description: entry.description,
      lines: entry.lines,
      attachments: entry.attachments,
      status,
      createdAt: timestamp,
      createdBy: 'Current User',
      ...(status === 'Posted' && {
        postedAt: timestamp,
        postedBy: 'Current User'
      })
    };

    try {
      const existing = JSON.parse(localStorage.getItem('journalEntries') || '[]');
      const updated = [journalEntry, ...existing];
      localStorage.setItem('journalEntries', JSON.stringify(updated));
    } catch (err) {
      console.error('Error persisting journal entry:', err);
      throw err;
    }

    return journalEntry;
  };

  const saveDraft = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    setErrors({});

    try {
      if (!entry.description.trim()) {
        setErrors({ description: 'Description is required' });
        setIsSubmitting(false);
        return;
      }

      const saved = saveJournalEntry('Draft');
      setShowSuccess(true);
      setTimeout(() => router.push(`/journal/${saved.id}`), 1500);
    } catch (err) {
      console.error('Error saving draft:', err);
      setErrors({ general: 'Failed to save draft. Please try again.' });
      setIsSubmitting(false);
    }
  };

  const postEntry = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    setErrors({});

    try {
      if (!validateEntry()) {
        setIsSubmitting(false);
        return;
      }

      const saved = saveJournalEntry('Posted');
      setShowSuccess(true);
      setTimeout(() => router.push(`/journal/${saved.id}`), 1500);
    } catch (err) {
      console.error('Error posting entry:', err);
      setErrors({ general: 'Failed to post entry. Please try again.' });
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(
      amount
    );

  const filteredAccounts = (search: string) =>
    chartOfAccounts.filter(
      acc =>
        acc.fullName.toLowerCase().includes(search.toLowerCase()) ||
        acc.code.includes(search)
    );

  const filteredPartners = (search: string) =>
    partners.filter(p => p.toLowerCase().includes(search.toLowerCase()));

  const filteredCostCenters = (search: string) =>
    costCenters.filter(c => c.toLowerCase().includes(search.toLowerCase()));

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main
        className={`transition-all duration-300 p-6 ${
          navMode === 'topnav' ? '' : sidebarOpen ? 'pl-64' : 'pl-16'
        }`}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header Controls */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  New Journal Entry
                </h1>
                <p className="text-gray-600">
                  Create a new accounting journal entry
                </p>
              </div>

              <div className="flex gap-3">
                <Link
                  href="/journal"
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 whitespace-nowrap"
                >
                  Cancel
                </Link>

                <button
                  onClick={saveDraft}
                  disabled={isSubmitting}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                >
                  {isSubmitting ? 'Saving...' : 'Save Draft'}
                </button>

                <button
                  onClick={postEntry}
                  disabled={!totals.isBalanced || isSubmitting}
                  className={`px-4 py-2 rounded-lg whitespace-nowrap ${
                    totals.isBalanced && !isSubmitting
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {isSubmitting ? 'Posting...' : 'Post Entry'}
                </button>
              </div>
            </div>

            {/* Success & General Errors */}
            {showSuccess && (
              <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-check-line w-5 h-5 flex items-center justify-center text-green-600 mr-3"></i>
                  <span className="text-green-800 font-medium">
                    Journal entry saved successfully! Redirecting...
                  </span>
                </div>
              </div>
            )}

            {errors.general && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-error-warning-line w-5 h-5 flex items-center justify-center text-red-600 mr-3"></i>
                  <span className="text-red-800 font-medium">{errors.general}</span>
                </div>
              </div>
            )}
          </div>

          {/* Main Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column (Entry details) */}
            <div className="lg:col-span-2 space-y-6">
              {/* Entry Header */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Entry Header
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Entry ID (generated) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Entry ID
                    </label>
                    <input
                      type="text"
                      value={generateEntryId()}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 text-sm"
                    />
                  </div>

                  {/* Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Date <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={entry.date}
                      onChange={e => updateEntry('date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    />
                  </div>

                  {/* Journal Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Journal Type <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={entry.journalType}
                      onChange={e => updateEntry('journalType', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8 ${
                        errors.journalType ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select journal type...</option>
                      {journalTypes.map(t => (
                        <option key={t} value={t}>
                          {t}
                        </option>
                      ))}
                    </select>
                    {errors.journalType && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.journalType}
                      </p>
                    )}
                  </div>

                  {/* Reference */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Reference
                    </label>
                    <input
                      type="text"
                      value={entry.reference}
                      onChange={e => updateEntry('reference', e.target.value)}
                      placeholder="Invoice #, PO #, etc."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    />
                  </div>
                </div>

                {/* Description */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={entry.description}
                    onChange={e => updateEntry('description', e.target.value)}
                    placeholder="Brief description of the journal entry..."
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm ${
                      errors.description ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.description && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Journal Lines */}
              <div className="bg-white p-6 rounded-lg shadow">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Journal Lines
                  </h2>
                  <button
                    onClick={addLine}
                    className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    Add Line
                  </button>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Account
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Partner
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Label
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Description
                        </th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Cost Center
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase">
                          Debit
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase">
                          Credit
                        </th>
                        <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {entry.lines.map((line, idx) => (
                        <tr key={line.id}>
                          {/* Account */}
                          <td className="px-3 py-2">
                            <div className="relative">
                              <input
                                type="text"
                                value={line.account}
                                onChange={e =>
                                  updateLine(line.id, 'account', e.target.value)
                                }
                                onFocus={() => setShowAccountDropdown(line.id)}
                                onBlur={() => setTimeout(() => setShowAccountDropdown(null), 200)}
                                placeholder="Select account..."
                                className={`w-full px-3 py-2 border rounded text-sm ${
                                  errors[`line_${idx}_account`]
                                    ? 'border-red-300'
                                    : 'border-gray-300'
                                }`}
                              />
                              {showAccountDropdown === line.id && (
                                <div className="absolute z-10 w-80 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                                  {filteredAccounts(line.account).map(acc => (
                                    <button
                                      key={acc.code}
                                      onMouseDown={() => {
                                        updateLine(line.id, 'account', acc.fullName);
                                        setShowAccountDropdown(null);
                                      }}
                                      className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0"
                                    >
                                      <div className="font-medium text-gray-900">
                                        {acc.code} - {acc.name}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {acc.fullName}
                                      </div>
                                    </button>
                                  ))}
                                </div>
                              )}
                              {errors[`line_${idx}_account`] && (
                                <p className="text-red-500 text-xs mt-1">
                                  {errors[`line_${idx}_account`]}
                                </p>
                              )}
                            </div>
                          </td>

                          {/* Partner */}
                          <td className="px-3 py-2">
                            <div className="relative">
                              <input
                                type="text"
                                value={line.partner}
                                onChange={e =>
                                  updateLine(line.id, 'partner', e.target.value)
                                }
                                onFocus={() => setShowPartnerDropdown(line.id)}
                                onBlur={() => setTimeout(() => setShowPartnerDropdown(null), 200)}
                                placeholder="Select partner..."
                                className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                              />
                              {showPartnerDropdown === line.id && (
                                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                                  {filteredPartners(line.partner).map(p => (
                                    <button
                                      key={p}
                                      onMouseDown={() => {
                                        updateLine(line.id, 'partner', p);
                                        setShowPartnerDropdown(null);
                                      }}
                                      className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer"
                                    >
                                      {p}
                                    </button>
                                  ))}
                                </div>
                              )}
                            </div>
                          </td>

                          {/* Label */}
                          <td className="px-3 py-2">
                            <input
                              type="text"
                              value={line.label}
                              onChange={e =>
                                updateLine(line.id, 'label', e.target.value)
                              }
                              placeholder="Transaction label..."
                              className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                            />
                          </td>

                          {/* Description */}
                          <td className="px-3 py-2">
                            <input
                              type="text"
                              value={line.description}
                              onChange={e =>
                                updateLine(line.id, 'description', e.target.value)
                              }
                              placeholder="Line description..."
                              className={`w-full px-3 py-2 border rounded text-sm ${
                                errors[`line_${idx}_description`]
                                  ? 'border-red-300'
                                  : 'border-gray-300'
                              }`}
                            />
                            {errors[`line_${idx}_description`] && (
                              <p className="text-red-500 text-xs mt-1">
                                {errors[`line_${idx}_description`]}
                              </p>
                            )}
                          </td>

                          {/* Cost Center */}
                          <td className="px-3 py-2">
                            <div className="relative">
                              <input
                                type="text"
                                value={line.costCenter}
                                onChange={e =>
                                  updateLine(line.id, 'costCenter', e.target.value)
                                }
                                onFocus={() => setShowCostCenterDropdown(line.id)}
                                onBlur={() => setTimeout(() => setShowCostCenterDropdown(null), 200)}
                                placeholder="Optional..."
                                className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                              />
                              {showCostCenterDropdown === line.id && (
                                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                                  {filteredCostCenters(line.costCenter).map(cc => (
                                    <button
                                      key={cc}
                                      onMouseDown={() => {
                                        updateLine(line.id, 'costCenter', cc);
                                        setShowCostCenterDropdown(null);
                                      }}
                                      className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer"
                                    >
                                      {cc}
                                    </button>
                                  ))}
                                </div>
                              )}
                            </div>
                          </td>

                          {/* Debit */}
                          <td className="px-3 py-2">
                            <div className="relative">
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs">
                                $
                              </span>
                              <input
                                type="number"
                                value={line.debit || ''}
                                onChange={e => {
                                  const val = parseFloat(e.target.value) || 0;
                                  updateLine(line.id, 'debit', val);
                                  if (val > 0) updateLine(line.id, 'credit', 0);
                                }}
                                placeholder="0.00"
                                step="0.01"
                                className="w-full pl-6 pr-3 py-2 border border-gray-300 rounded text-right text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </td>

                          {/* Credit */}
                          <td className="px-3 py-2">
                            <div className="relative">
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs">
                                $
                              </span>
                              <input
                                type="number"
                                value={line.credit || ''}
                                onChange={e => {
                                  const val = parseFloat(e.target.value) || 0;
                                  updateLine(line.id, 'credit', val);
                                  if (val > 0) updateLine(line.id, 'debit', 0);
                                }}
                                placeholder="0.00"
                                step="0.01"
                                className="w-full pl-6 pr-3 py-2 border border-gray-300 rounded text-right text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </td>

                          {/* Action */}
                          <td className="px-3 py-2 text-center">
                            {entry.lines.length > 2 && (
                              <button
                                onClick={() => removeLine(line.id)}
                                className="text-red-600 hover:text-red-800 cursor-pointer p-1 rounded hover:bg-red-50"
                                title="Remove line"
                              >
                                <div className="w-6 h-6 flex items-center justify-center">
                                  <i className="ri-delete-bin-line w-4 h-4 flex items-center justify-center"></i>
                                </div>
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-gray-50 border-t">
                      <tr>
                        <td colSpan={5} className="px-3 py-3 text-right font-medium text-gray-900">
                          <div className="flex items-center justify-end space-x-4">
                            <span>Totals:</span>
                            <div
                              className={`px-3 py-1 rounded-full text-xs font-medium ${
                                totals.isBalanced
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}
                            >
                              {totals.isBalanced ? 'Balanced' : 'Unbalanced'}
                            </div>
                          </div>
                        </td>
                        <td className="px-3 py-3 text-right font-bold text-blue-600">
                          {formatCurrency(totals.debit)}
                        </td>
                        <td className="px-3 py-3 text-right font-bold text-green-600">
                          {formatCurrency(totals.credit)}
                        </td>
                        <td className="px-3 py-3 text-center">
                          {totals.isBalanced ? (
                            <i className="ri-check-line text-green-600 text-lg w-5 h-5 flex items-center justify-center"></i>
                          ) : (
                            <i className="ri-close-line text-red-600 text-lg w-5 h-5 flex items-center justify-center"></i>
                          )}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                {errors.balance && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-700 text-sm font-medium">{errors.balance}</p>
                    <p className="text-red-600 text-xs mt-1">
                      Difference: {formatCurrency(Math.abs(totals.debit - totals.credit))}
                    </p>
                  </div>
                )}
              </div>

              {/* Attachments (remains inside left column) */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-lg font-semibold text-gray-900">Attachments</h2>

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload"
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer flex flex-col items-center gap-2">
                    <i className="ri-upload-cloud-line text-3xl text-gray-400 w-12 h-12 flex items-center justify-center mx-auto"></i>
                    <span className="text-gray-600">Click to upload documents</span>
                    <span className="text-xs text-gray-500">PDF, Images, Word documents</span>
                  </label>
                </div>

                {entry.attachments.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {entry.attachments.map((file, idx) => (
                      <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <i className="ri-file-line text-gray-500 w-5 h-5 flex items-center justify-center"></i>
                          <span className="text-sm text-gray-900">{file.name}</span>
                          <span className="text-xs text-gray-500">
                            ({(file.size / 1024).toFixed(1)} KB)
                          </span>
                        </div>
                        <button
                          onClick={() => removeAttachment(idx)}
                          className="text-red-600 hover:text-red-800 cursor-pointer"
                        >
                          <div className="w-6 h-6 flex items-center justify-center">
                            <i className="ri-close-line w-4 h-4 flex items-center justify-center"></i>
                          </div>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Right Column (Summary & Tips) */}
            <div className="space-y-6">
              {/* Entry Summary */}
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Entry Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Debits:</span>
                    <span className="font-medium">{formatCurrency(totals.debit)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Credits:</span>
                    <span className="font-medium">{formatCurrency(totals.credit)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-3">
                    <span className="text-gray-600">Difference:</span>
                    <span className={`font-medium ${totals.isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(Math.abs(totals.debit - totals.credit))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`font-medium ${totals.isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                      {totals.isBalanced ? 'Balanced' : 'Unbalanced'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* New Footer Controls */}
        <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => router.push('/journal')}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={saveDraft}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 cursor-pointer whitespace-nowrap"
          >
            {isSubmitting ? 'Saving...' : 'Save as Draft'}
          </button>
          <button
            type="submit"
            onClick={postEntry}
            disabled={isSubmitting || !totals.isBalanced}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                <span>Posting...</span>
              </>
            ) : (
              <>
                <i className="ri-check-line w-4 h-4 flex items-center justify-center"></i>
                <span>Post Entry</span>
              </>
            )}
          </button>
        </div>
      </main>
    </div>
  );
}
