
'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Link from 'next/link';

interface ReportData {
  account: string;
  debit: number;
  credit: number;
  balance: number;
}

interface JournalBookEntry {
  id: string;
  date: string;
  reference: string;
  description: string;
  debit: number;
  credit: number;
}

export default function JournalReportsClient() {
  const [activeReport, setActiveReport] = useState('journal-book');
  const [dateFrom, setDateFrom] = useState('2024-01-01');
  const [dateTo, setDateTo] = useState('2024-01-31');
  const [selectedAccount, setSelectedAccount] = useState('');
  const [journalBookData, setJournalBookData] = useState<JournalBookEntry[]>([]);
  const [trialBalanceData, setTrialBalanceData] = useState<ReportData[]>([]);
  const [accountLedgerData, setAccountLedgerData] = useState<JournalBookEntry[]>([]);

  const accounts = [
    '1000 - Cash',
    '1100 - Accounts Receivable', 
    '1200 - Inventory',
    '2000 - Accounts Payable',
    '3000 - Owner\'s Equity',
    '4000 - Sales Revenue',
    '5000 - Cost of Goods Sold',
    '6000 - Operating Expenses'
  ];

  useEffect(() => {
    // Mock data for reports
    const mockJournalBook: JournalBookEntry[] = [
      {
        id: 'JE001',
        date: '2024-01-15',
        reference: 'INV-2024-001',
        description: 'Sale to Customer ABC',
        debit: 1200,
        credit: 0
      },
      {
        id: 'JE001',
        date: '2024-01-15',
        reference: 'INV-2024-001',
        description: 'Sales Revenue',
        debit: 0,
        credit: 1200
      },
      {
        id: 'JE002',
        date: '2024-01-16',
        reference: 'PO-2024-005',
        description: 'Office supplies purchase',
        debit: 350,
        credit: 0
      },
      {
        id: 'JE002',
        date: '2024-01-16',
        reference: 'PO-2024-005',
        description: 'Accounts Payable',
        debit: 0,
        credit: 350
      }
    ];

    const mockTrialBalance: ReportData[] = [
      { account: '1000 - Cash', debit: 15000, credit: 0, balance: 15000 },
      { account: '1100 - Accounts Receivable', debit: 8500, credit: 1200, balance: 7300 },
      { account: '1200 - Inventory', debit: 12000, credit: 0, balance: 12000 },
      { account: '2000 - Accounts Payable', debit: 0, credit: 3500, balance: -3500 },
      { account: '3000 - Owner\'s Equity', debit: 0, credit: 25000, balance: -25000 },
      { account: '4000 - Sales Revenue', debit: 0, credit: 8200, balance: -8200 },
      { account: '6000 - Operating Expenses', debit: 2400, credit: 0, balance: 2400 }
    ];

    const mockAccountLedger: JournalBookEntry[] = [
      {
        id: 'JE001',
        date: '2024-01-15',
        reference: 'INV-2024-001',
        description: 'Sale to Customer ABC',
        debit: 1200,
        credit: 0
      },
      {
        id: 'JE003',
        date: '2024-01-17',
        reference: 'PAY-001',
        description: 'Payment received from Customer ABC',
        debit: 0,
        credit: 1200
      }
    ];

    setJournalBookData(mockJournalBook);
    setTrialBalanceData(mockTrialBalance);
    setAccountLedgerData(mockAccountLedger);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const exportReport = (format: 'pdf' | 'excel') => {
    // Export logic here
    console.log(`Exporting ${activeReport} as ${format}`);
  };

  const printReport = () => {
    window.print();
  };

  const renderJournalBook = () => (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Journal Book</h2>
        <div className="flex gap-2">
          <button
            onClick={() => exportReport('pdf')}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-file-pdf-line"></i>
            PDF
          </button>
          <button
            onClick={() => exportReport('excel')}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-file-excel-line"></i>
            Excel
          </button>
          <button
            onClick={printReport}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-printer-line"></i>
            Print
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Date
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Entry ID
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Reference
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Description
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Debit
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Credit
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {journalBookData.map((entry, index) => (
              <tr key={index}>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {new Date(entry.date).toLocaleDateString()}
                </td>
                <td className="px-4 py-3 text-sm text-blue-600">
                  <Link href={`/journal/${entry.id}`} className="hover:underline cursor-pointer">
                    {entry.id}
                  </Link>
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {entry.reference}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {entry.description}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                  {entry.debit > 0 ? formatCurrency(entry.debit) : '-'}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                  {entry.credit > 0 ? formatCurrency(entry.credit) : '-'}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot className="bg-gray-50 border-t">
            <tr>
              <td colSpan={4} className="px-4 py-3 text-right font-medium text-gray-900">
                Totals:
              </td>
              <td className="px-4 py-3 text-right font-bold text-gray-900">
                {formatCurrency(journalBookData.reduce((sum, entry) => sum + entry.debit, 0))}
              </td>
              <td className="px-4 py-3 text-right font-bold text-gray-900">
                {formatCurrency(journalBookData.reduce((sum, entry) => sum + entry.credit, 0))}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  );

  const renderTrialBalance = () => (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Trial Balance</h2>
        <div className="flex gap-2">
          <button
            onClick={() => exportReport('pdf')}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-file-pdf-line"></i>
            PDF
          </button>
          <button
            onClick={() => exportReport('excel')}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-file-excel-line"></i>
            Excel
          </button>
          <button
            onClick={printReport}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-printer-line"></i>
            Print
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Account
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Debit
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Credit
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                Balance
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {trialBalanceData.map((account, index) => (
              <tr key={index}>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {account.account}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                  {account.debit > 0 ? formatCurrency(account.debit) : '-'}
                </td>
                <td className="px-4 py-3 text-sm text-gray-900 text-right">
                  {account.credit > 0 ? formatCurrency(account.credit) : '-'}
                </td>
                <td className={`px-4 py-3 text-sm text-right font-medium ${
                  account.balance >= 0 ? 'text-gray-900' : 'text-red-600'
                }`}>
                  {formatCurrency(Math.abs(account.balance))}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot className="bg-gray-50 border-t">
            <tr>
              <td className="px-4 py-3 text-right font-medium text-gray-900">
                Totals:
              </td>
              <td className="px-4 py-3 text-right font-bold text-gray-900">
                {formatCurrency(trialBalanceData.reduce((sum, account) => sum + account.debit, 0))}
              </td>
              <td className="px-4 py-3 text-right font-bold text-gray-900">
                {formatCurrency(trialBalanceData.reduce((sum, account) => sum + account.credit, 0))}
              </td>
              <td className="px-4 py-3 text-right font-bold text-gray-900">
                -
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  );

  const renderAccountLedger = () => (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">
          Account Ledger {selectedAccount && `- ${selectedAccount}`}
        </h2>
        <div className="flex gap-2">
          <button
            onClick={() => exportReport('pdf')}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-file-pdf-line"></i>
            PDF
          </button>
          <button
            onClick={() => exportReport('excel')}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-file-excel-line"></i>
            Excel
          </button>
          <button
            onClick={printReport}
            className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap text-sm"
          >
            <i className="ri-printer-line"></i>
            Print
          </button>
        </div>
      </div>

      {!selectedAccount ? (
        <div className="text-center py-12">
          <i className="ri-account-book-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Account</h3>
          <p className="text-gray-500 mb-4">Choose an account from the filter above to view its ledger.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Entry ID
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Reference
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Description
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                  Debit
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                  Credit
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                  Balance
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {accountLedgerData.map((entry, index) => {
                const runningBalance = accountLedgerData
                  .slice(0, index + 1)
                  .reduce((sum, e) => sum + e.debit - e.credit, 0);
                
                return (
                  <tr key={index}>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {new Date(entry.date).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-3 text-sm text-blue-600">
                      <Link href={`/journal/${entry.id}`} className="hover:underline cursor-pointer">
                        {entry.id}
                      </Link>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {entry.reference}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {entry.description}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 text-right">
                      {entry.debit > 0 ? formatCurrency(entry.debit) : '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 text-right">
                      {entry.credit > 0 ? formatCurrency(entry.credit) : '-'}
                    </td>
                    <td className={`px-4 py-3 text-sm text-right font-medium ${
                      runningBalance >= 0 ? 'text-gray-900' : 'text-red-600'
                    }`}>
                      {formatCurrency(Math.abs(runningBalance))}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <Link
                href="/journal"
                className="text-gray-600 hover:text-gray-800 cursor-pointer"
              >
                <div className="w-8 h-8 flex items-center justify-center">
                  <i className="ri-arrow-left-line text-xl"></i>
                </div>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Journal Reports</h1>
                <p className="text-gray-600">Financial reports and analysis</p>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveReport('journal-book')}
                className={`px-4 py-2 rounded-md text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeReport === 'journal-book'
                    ? 'bg-white text-gray-900 shadow'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Journal Book
              </button>
              <button
                onClick={() => setActiveReport('trial-balance')}
                className={`px-4 py-2 rounded-md text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeReport === 'trial-balance'
                    ? 'bg-white text-gray-900 shadow'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Trial Balance
              </button>
              <button
                onClick={() => setActiveReport('account-ledger')}
                className={`px-4 py-2 rounded-md text-sm font-medium cursor-pointer whitespace-nowrap ${
                  activeReport === 'account-ledger'
                    ? 'bg-white text-gray-900 shadow'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Account Ledger
              </button>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date From
                </label>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date To
                </label>
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
              {activeReport === 'account-ledger' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account
                  </label>
                  <select
                    value={selectedAccount}
                    onChange={(e) => setSelectedAccount(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                  >
                    <option value="">Select account...</option>
                    {accounts.map(account => (
                      <option key={account} value={account}>{account}</option>
                    ))}
                  </select>
                </div>
              )}
              <div className="flex items-end">
                <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap">
                  Generate Report
                </button>
              </div>
            </div>
          </div>
        </div>

        {activeReport === 'journal-book' && renderJournalBook()}
        {activeReport === 'trial-balance' && renderTrialBalance()}
        {activeReport === 'account-ledger' && renderAccountLedger()}
      </div>
    </div>
  );
}
