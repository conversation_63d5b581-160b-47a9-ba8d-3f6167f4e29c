'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import { useTranslation } from '@/hooks/useTranslation';

interface ProductRow {
  id: string;
  name: string;
  supplier: string;
  supplierPrice: string;
  salesPrice: string;
  quantity: string;
  quantityUnit: string;
  photo: File | null;
  photoPreview: string;
  customData: Record<string, string>;
}

interface CustomColumn {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'dropdown';
  options?: string[];
}

function NewProductForm() {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');
  const [productRows, setProductRows] = useState<ProductRow[]>([
    {
      id: 'row-1',
      name: '',
      supplier: '',
      supplierPrice: '',
      salesPrice: '',
      quantity: '1',
      quantityUnit: 'pcs',
      photo: null,
      photoPreview: '',
      customData: {}
    }
  ]);
  
  const [customColumns, setCustomColumns] = useState<CustomColumn[]>([]);
  const [globalQuantityUnit, setGlobalQuantityUnit] = useState('pcs');
  const [showAddColumnModal, setShowAddColumnModal] = useState(false);
  const [newColumnName, setNewColumnName] = useState('');
  const [newColumnType, setNewColumnType] = useState<'text' | 'number' | 'date' | 'dropdown'>('text');
  const [newColumnOptions, setNewColumnOptions] = useState('');
  
  // Paste functionality states
  const [showPasteModal, setShowPasteModal] = useState(false);
  const [pasteData, setPasteData] = useState('');
  const [pastePreview, setPastePreview] = useState<string[][]>([]);
  const [columnMapping, setColumnMapping] = useState<Record<number, string>>({});
  
  // Navigation mode detection
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  // Load custom columns from localStorage
  useEffect(() => {
    const savedColumns = localStorage.getItem('customColumns');
    if (savedColumns) {
      try {
        const columns = JSON.parse(savedColumns);
        setCustomColumns(columns);
        
        // Initialize custom data for existing rows
        setProductRows(prevRows => 
          prevRows.map(row => ({
            ...row,
            customData: {
              ...row.customData,
              ...columns.reduce((acc: Record<string, string>, col: CustomColumn) => {
                if (!(col.id in row.customData)) {
                  acc[col.id] = '';
                }
                return acc;
              }, {})
            }
          }))
        );
      } catch (error) {
        console.error('Error loading custom columns:', error);
      }
    }
  }, []);

  // Handle pre-filled customer from URL params
  useEffect(() => {
    const customer = searchParams.get('customer');
    if (customer) {
      // You can use customer parameter here if needed
      console.log('Pre-filled customer:', customer);
    }
  }, [searchParams]);

  const addProductRow = () => {
    const newRow: ProductRow = {
      id: `row-${Date.now()}`,
      name: '',
      supplier: '',
      supplierPrice: '',
      salesPrice: '',
      quantity: '1',
      quantityUnit: globalQuantityUnit,
      photo: null,
      photoPreview: '',
      customData: customColumns.reduce((acc, col) => {
        acc[col.id] = '';
        return acc;
      }, {} as Record<string, string>)
    };
    setProductRows([...productRows, newRow]);
  };

  const removeProductRow = (id: string) => {
    if (productRows.length > 1) {
      setProductRows(productRows.filter(row => row.id !== id));
    }
  };

  const updateProductRow = (id: string, field: keyof ProductRow | string, value: string | File | null) => {
    setProductRows(productRows.map(row => {
      if (row.id === id) {
        if (field.startsWith('custom-')) {
          return {
            ...row,
            customData: {
              ...row.customData,
              [field]: value as string
            }
          };
        } else if (field === 'photo') {
          const file = value as File | null;
          let photoPreview = '';
          if (file) {
            photoPreview = URL.createObjectURL(file);
          }
          return {
            ...row,
            photo: file,
            photoPreview
          };
        } else {
          return {
            ...row,
            [field]: value
          };
        }
      }
      return row;
    }));
  };

  const updateGlobalQuantityUnit = (unit: string) => {
    setGlobalQuantityUnit(unit);
    setProductRows(productRows.map(row => ({
      ...row,
      quantityUnit: unit
    })));
  };

  const addCustomColumn = () => {
    if (!newColumnName.trim()) return;

    const newColumn: CustomColumn = {
      id: `custom-${Date.now()}`,
      name: newColumnName.trim(),
      type: newColumnType,
      options: newColumnType === 'dropdown' ? newColumnOptions.split(',').map(opt => opt.trim()).filter(opt => opt) : undefined
    };

    const updatedColumns = [...customColumns, newColumn];
    setCustomColumns(updatedColumns);
    
    // Save to localStorage
    localStorage.setItem('customColumns', JSON.stringify(updatedColumns));
    
    // Initialize the new column data for all existing rows
    setProductRows(productRows.map(row => ({
      ...row,
      customData: {
        ...row.customData,
        [newColumn.id]: ''
      }
    })));

    // Reset form
    setNewColumnName('');
    setNewColumnType('text');
    setNewColumnOptions('');
    setShowAddColumnModal(false);
  };

  const removeCustomColumn = (columnId: string) => {
    const updatedColumns = customColumns.filter(col => col.id !== columnId);
    setCustomColumns(updatedColumns);
    
    // Save to localStorage
    localStorage.setItem('customColumns', JSON.stringify(updatedColumns));
    
    // Remove the column data from all rows
    setProductRows(productRows.map(row => {
      const { [columnId]: removed, ...restCustomData } = row.customData;
      return {
        ...row,
        customData: restCustomData
      };
    }));
  };

  const processPasteData = (data: string) => {
    if (!data.trim()) {
      setPastePreview([]);
      return;
    }

    // Handle different paste formats
    let rows: string[][] = [];
    
    // Check if data contains tabs (Excel table format)
    if (data.includes('\t')) {
      // Split by lines and then by tabs (standard Excel copy-paste)
      const lines = data.trim().split('\n');
      rows = lines.map(line => line.split('\t'));
    } else if (data.includes(',')) {
      // Handle CSV format
      const lines = data.trim().split('\n');
      rows = lines.map(line => {
        // Simple CSV parsing (handles basic cases)
        return line.split(',').map(cell => cell.trim().replace(/^["']|["']$/g, ''));
      });
    } else {
      // Handle single column or space-separated data
      const lines = data.trim().split('\n');
      if (lines.length > 1) {
        // Multiple lines - treat each line as a row with single column
        rows = lines.map(line => [line.trim()]);
      } else {
        // Single line - split by spaces or treat as single cell
        const items = data.trim().split(/\s+/);
        if (items.length > 1) {
          // Multiple items - each item becomes a row
          rows = items.map(item => [item.trim()]);
        } else {
          // Single item
          rows = [[data.trim()]];
        }
      }
    }
    
    // Remove empty rows
    const filteredRows = rows.filter(row => row.some(cell => cell && cell.trim() !== ''));
    
    setPastePreview(filteredRows);
    
    // Auto-map columns based on common patterns
    if (filteredRows.length > 0) {
      const headerRow = filteredRows[0];
      const autoMapping: Record<number, string> = {};
      
      headerRow.forEach((header, index) => {
        const lowerHeader = header.toLowerCase().trim();
        
        // Auto-detect common patterns
        if (lowerHeader.includes('product') || lowerHeader.includes('name') || lowerHeader.includes('item')) {
          autoMapping[index] = 'name';
        } else if (lowerHeader.includes('supplier') || lowerHeader.includes('vendor')) {
          autoMapping[index] = 'supplier';
        } else if (lowerHeader.includes('supplier price') || lowerHeader.includes('cost')) {
          autoMapping[index] = 'supplierPrice';
        } else if (lowerHeader.includes('sales price') || lowerHeader.includes('price') || lowerHeader.includes('sell')) {
          autoMapping[index] = 'salesPrice';
        } else if (lowerHeader.includes('quantity') || lowerHeader.includes('qty')) {
          autoMapping[index] = 'quantity';
        } else if (lowerHeader.includes('sku')) {
          // Map to first available custom column or create one
          const skuColumn = customColumns.find(col => col.name.toLowerCase().includes('sku'));
          if (skuColumn) {
            autoMapping[index] = skuColumn.id;
          }
        } else if (filteredRows.length > 1) {
          // If we have multiple rows and this looks like product data, assume first column is product name
          if (index === 0) {
            autoMapping[index] = 'name';
          }
        }
      });
      
      // If no auto-mapping detected and we have data, suggest mapping first column to product name
      if (Object.keys(autoMapping).length === 0 && filteredRows.length > 0) {
        autoMapping[0] = 'name';
      }
      
      setColumnMapping(autoMapping);
    }
  };

  const applyPasteData = () => {
    if (pastePreview.length === 0) return;
    
    // Determine if first row is header based on column mapping
    const hasHeader = Object.keys(columnMapping).length > 0 && 
      pastePreview.length > 1 && 
      pastePreview[0].some(cell => 
        cell.toLowerCase().includes('product') || 
        cell.toLowerCase().includes('name') || 
        cell.toLowerCase().includes('price') ||
        cell.toLowerCase().includes('supplier')
      );
    
    const dataRows = hasHeader ? pastePreview.slice(1) : pastePreview;
    
    if (dataRows.length === 0) return;
    
    // Create new product rows from paste data
    const newRows = dataRows.map((row, index) => {
      const newRow: ProductRow = {
        id: `row-${Date.now()}-${index}`,
        name: '',
        supplier: '',
        supplierPrice: '',
        salesPrice: '',
        quantity: '1',
        quantityUnit: globalQuantityUnit,
        photo: null,
        photoPreview: '',
        customData: {} as Record<string, string>
      };
      
      // Initialize custom data for all existing custom columns
      customColumns.forEach(col => {
        newRow.customData[col.id] = '';
      });
      
      // Map data based on column mapping
      Object.entries(columnMapping).forEach(([colIndex, fieldName]) => {
        const cellValue = row[parseInt(colIndex)] || '';
        const trimmedValue = cellValue.trim();
        
        if (fieldName === 'name') {
          newRow.name = trimmedValue;
        } else if (fieldName === 'supplier') {
          newRow.supplier = trimmedValue;
        } else if (fieldName === 'supplierPrice') {
          // Clean price data - remove currency symbols and keep only numbers and decimal point
          const cleanPrice = trimmedValue.replace(/[^0-9.]/g, '');
          newRow.supplierPrice = cleanPrice;
        } else if (fieldName === 'salesPrice') {
          // Clean price data - remove currency symbols and keep only numbers and decimal point
          const cleanPrice = trimmedValue.replace(/[^0-9.]/g, '');
          newRow.salesPrice = cleanPrice;
        } else if (fieldName === 'quantity') {
          // Clean quantity data - keep only numbers and decimal point
          const cleanQty = trimmedValue.replace(/[^0-9.]/g, '');
          newRow.quantity = cleanQty || '1';
        } else if (fieldName.startsWith('custom-')) {
          // Handle custom column data
          newRow.customData[fieldName] = trimmedValue;
        }
      });
      
      // If no mapping was set, but we have data, try to intelligently fill
      if (Object.keys(columnMapping).length === 0 && row.length > 0) {
        // Use first column as product name
        newRow.name = row[0].trim();
        
        // If we have more columns, try to use them intelligently
        if (row.length > 1) {
          // Second column could be supplier
          newRow.supplier = row[1].trim();
        }
        if (row.length > 2) {
          // Third column could be price
          const priceValue = row[2].replace(/[^0-9.]/g, '');
          if (priceValue) {
            newRow.salesPrice = priceValue;
          }
        }
      }
      
      return newRow;
    });
    
    // Ask user whether to replace or append
    const replace = confirm(
      `Found ${newRows.length} products to import.\n\n` +
      `Click OK to REPLACE existing products\n` +
      `Click Cancel to ADD to existing products`
    );
    
    if (replace) {
      setProductRows(newRows);
    } else {
      setProductRows(prev => [...prev, ...newRows]);
    }
    
    setShowPasteModal(false);
    setPasteData('');
    setPastePreview([]);
    setColumnMapping({});
    
    // Show success message
    alert(`Successfully imported ${newRows.length} products!`);
  };

  const handleSave = () => {
    // Validate required fields
    const invalidRows = productRows.filter(row => !row.name.trim());
    if (invalidRows.length > 0) {
      alert('Please fill in product names for all rows');
      return;
    }

    // Save to localStorage or your preferred storage
    const savedData = {
      products: productRows,
      customColumns: customColumns,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('newProductData', JSON.stringify(savedData));
    alert('Products saved successfully!');
    
    // Redirect to products page
    router.push('/products');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <main className={`transition-all duration-300 p-6 ${navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')}`}>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/" className="hover:text-gray-700 cursor-pointer">
                Dashboard
              </Link>
              <i className="ri-arrow-right-s-line w-4 h-4 flex items-center justify-center"></i>
              <span className="text-gray-900 font-medium">New Products</span>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Add New Products</h1>
                <p className="text-gray-600">Create and manage your product inventory</p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowPasteModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-clipboard-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Paste from Excel</span>
                </button>
                <button
                  onClick={handleSave}
                  className="flex items-center space-x-2 px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-save-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Save Products</span>
                </button>
              </div>
            </div>
          </div>

          {/* Global Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Global Settings</h3>
            <div className="flex items-center space-x-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Quantity Unit
                </label>
                <select
                  value={globalQuantityUnit}
                  onChange={(e) => updateGlobalQuantityUnit(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                >
                  <option value="pcs">Pieces (pcs)</option>
                  <option value="kg">Kilograms (kg)</option>
                  <option value="lbs">Pounds (lbs)</option>
                  <option value="boxes">Boxes</option>
                  <option value="units">Units</option>
                  <option value="sets">Sets</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Columns
                </label>
                <button
                  onClick={() => setShowAddColumnModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Add Column</span>
                </button>
              </div>
            </div>
            
            {customColumns.length > 0 && (
              <div className="mt-4">
                <div className="flex flex-wrap gap-2">
                  {customColumns.map((column) => (
                    <div key={column.id} className="flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-full">
                      <span className="text-sm text-gray-700">{column.name}</span>
                      <span className="text-xs text-gray-500">({column.type})</span>
                      <button
                        onClick={() => removeCustomColumn(column.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <i className="ri-close-line w-3 h-3 flex items-center justify-center"></i>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Products Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Product Details</h3>
                <button
                  onClick={addProductRow}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 cursor-pointer whitespace-nowrap"
                >
                  <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                  <span>Add Row</span>
                </button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Product Name *
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Supplier
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Supplier Price
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Sales Price
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Photo
                    </th>
                    {customColumns.map((column) => (
                      <th key={column.id} className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        {column.name}
                      </th>
                    ))}
                    <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {productRows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={row.name}
                          onChange={(e) => updateProductRow(row.id, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                          placeholder="Product name"
                          required
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={row.supplier}
                          onChange={(e) => updateProductRow(row.id, 'supplier', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                          placeholder="Supplier name"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={row.supplierPrice}
                            onChange={(e) => updateProductRow(row.id, 'supplierPrice', e.target.value)}
                            className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                            placeholder="0.00"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={row.salesPrice}
                            onChange={(e) => updateProductRow(row.id, 'salesPrice', e.target.value)}
                            className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                            placeholder="0.00"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={row.quantity}
                            onChange={(e) => updateProductRow(row.id, 'quantity', e.target.value)}
                            className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                            placeholder="1"
                          />
                          <span className="text-sm text-gray-500">{row.quantityUnit}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => updateProductRow(row.id, 'photo', e.target.files?.[0] || null)}
                            className="hidden"
                            id={`photo-${row.id}`}
                          />
                          <label
                            htmlFor={`photo-${row.id}`}
                            className="cursor-pointer flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 bg-gray-50 border border-gray-300 rounded-lg hover:bg-gray-100"
                          >
                            <i className="ri-camera-line w-4 h-4 flex items-center justify-center"></i>
                            <span>{row.photo ? 'Change' : 'Upload'}</span>
                          </label>
                          {row.photoPreview && (
                            <img
                              src={row.photoPreview}
                              alt="Preview"
                              className="w-10 h-10 object-cover rounded-lg border border-gray-300"
                            />
                          )}
                        </div>
                      </td>
                      {customColumns.map((column) => (
                        <td key={column.id} className="px-6 py-4 whitespace-nowrap">
                          {column.type === 'dropdown' && column.options ? (
                            <select
                              value={row.customData[column.id] || ''}
                              onChange={(e) => updateProductRow(row.id, column.id, e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                            >
                              <option value="">Select...</option>
                              {column.options.map((option) => (
                                <option key={option} value={option}>{option}</option>
                              ))}
                            </select>
                          ) : (
                            <input
                              type={column.type === 'number' ? 'number' : column.type === 'date' ? 'date' : 'text'}
                              value={row.customData[column.id] || ''}
                              onChange={(e) => updateProductRow(row.id, column.id, e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                              placeholder={`Enter ${column.name.toLowerCase()}`}
                            />
                          )}
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <button
                          onClick={() => removeProductRow(row.id)}
                          disabled={productRows.length === 1}
                          className={`text-red-600 hover:text-red-800 disabled:text-gray-400 disabled:cursor-not-allowed cursor-pointer`}
                        >
                          <i className="ri-delete-bin-line w-5 h-5 flex items-center justify-center"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Add Custom Column Modal */}
          {showAddColumnModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Add Custom Column</h3>
                  <button
                    onClick={() => setShowAddColumnModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Column Name
                    </label>
                    <input
                      type="text"
                      value={newColumnName}
                      onChange={(e) => setNewColumnName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., SKU, Brand, Category"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Column Type
                    </label>
                    <select
                      value={newColumnType}
                      onChange={(e) => setNewColumnType(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-8"
                    >
                      <option value="text">Text</option>
                      <option value="number">Number</option>
                      <option value="date">Date</option>
                      <option value="dropdown">Dropdown</option>
                    </select>
                  </div>

                  {newColumnType === 'dropdown' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Dropdown Options (comma separated)
                      </label>
                      <textarea
                        value={newColumnOptions}
                        onChange={(e) => setNewColumnOptions(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={3}
                        placeholder="Option 1, Option 2, Option 3"
                      />
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-end space-x-3 mt-6">
                  <button
                    onClick={() => setShowAddColumnModal(false)}
                    className="px-4 py-2 text-gray-700 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={addCustomColumn}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    Add Column
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Paste Modal */}
          {showPasteModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Import from Excel/CSV</h3>
                  <button
                    onClick={() => setShowPasteModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Paste your data here (Excel, CSV, or plain text)
                    </label>
                    <textarea
                      value={pasteData}
                      onChange={(e) => {
                        setPasteData(e.target.value);
                        processPasteData(e.target.value);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={8}
                      placeholder="Copy and paste your product data here..."
                    />
                  </div>

                  {pastePreview.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Preview ({pastePreview.length} rows)</h4>
                      <div className="border border-gray-300 rounded-lg overflow-hidden">
                        <div className="overflow-x-auto max-h-60">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                {pastePreview[0]?.map((header, index) => (
                                  <th key={index} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                    Column {index + 1}
                                    <select
                                      value={columnMapping[index] || ''}
                                      onChange={(e) => setColumnMapping({...columnMapping, [index]: e.target.value})}
                                      className="block w-full mt-1 text-xs border-gray-300 rounded text-gray-700 pr-6"
                                    >
                                      <option value="">Skip</option>
                                      <option value="name">Product Name</option>
                                      <option value="supplier">Supplier</option>
                                      <option value="supplierPrice">Supplier Price</option>
                                      <option value="salesPrice">Sales Price</option>
                                      <option value="quantity">Quantity</option>
                                      {customColumns.map(col => (
                                        <option key={col.id} value={col.id}>{col.name}</option>
                                      ))}
                                    </select>
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {pastePreview.slice(0, 5).map((row, rowIndex) => (
                                <tr key={rowIndex}>
                                  {row.map((cell, cellIndex) => (
                                    <td key={cellIndex} className="px-4 py-2 text-sm text-gray-900 max-w-32 truncate">
                                      {cell}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                      {pastePreview.length > 5 && (
                        <p className="text-xs text-gray-500 mt-1">... and {pastePreview.length - 5} more rows</p>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-end space-x-3 mt-6">
                  <button
                    onClick={() => setShowPasteModal(false)}
                    className="px-4 py-2 text-gray-700 hover:text-gray-900 cursor-pointer whitespace-nowrap"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={applyPasteData}
                    disabled={pastePreview.length === 0}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap"
                  >
                    Import Data
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default function NewProductsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <NewProductForm />
    </Suspense>
  );
}