
'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import SupplierDropdown from '@/components/SupplierDropdown';
import ExcelImportModal from '@/components/ExcelImportModal';
import PurchaseStore, { CustomColumn } from '@/lib/purchaseStore';
import { useTranslation } from '@/hooks/useTranslation';

export default function NewPurchasePage() {
  const { t } = useTranslation();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [formData, setFormData] = useState({
    supplier: '',
    buyer: '',
    deliveryDate: '',
    status: 'Pending'
  });

  const [productRows, setProductRows] = useState([
    {
      id: 'row-1',
      name: '',
      unitPrice: '',
      quantity: '1',
      quantityUnit: 'pcs',
      photo: null as File | null,
      photoPreview: '',
      customData: {} as Record<string, string>
    }
  ]);

  const [customColumns, setCustomColumns] = useState<CustomColumn[]>([]);
  const [showColumnModal, setShowColumnModal] = useState(false);
  const [showExcelImportModal, setShowExcelImportModal] = useState(false);
  const [newColumn, setNewColumn] = useState({
    name: '',
    type: 'text' as const,
    required: false,
    options: ['']
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Add refs for paste areas
  const pasteAreaRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // -------------------------------------------------------------------------
  // Excel Import Handler
  // -------------------------------------------------------------------------
  const handleExcelImport = (importedProducts: any[]) => {
    const confirm = window.confirm(
      `Import ${importedProducts.length} products from Excel file?\n\nClick OK to REPLACE existing products\nClick Cancel to ADD to existing products`
    );

    if (confirm) {
      // Replace existing products
      setProductRows(importedProducts);
    } else {
      // Add to existing products
      setProductRows(prev => [...prev, ...importedProducts]);
    }

    alert(`Successfully imported ${importedProducts.length} products from Excel!`);
  };

  // -------------------------------------------------------------------------
  // Enhanced Direct paste handling (single field or multi‑line → create rows)
  // -------------------------------------------------------------------------
  const handleDirectPaste = (
    rowId: string,
    field: string,
    e: React.ClipboardEvent<HTMLInputElement>
  ) => {
    e.preventDefault();
    e.stopPropagation();

    const pastedText = e.clipboardData.getData('text');
    console.log('Pasting text:', pastedText, 'into field:', field);

    if (!pastedText || !pastedText.trim()) {
      console.log('No text to paste');
      return;
    }

    // Clean the pasted text and split by line breaks
    const lines = pastedText
      .trim()
      .split(/[\r\n]+/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    console.log('Processed lines:', lines, 'for field:', field);

    if (lines.length === 0) {
      console.log('No valid lines found');
      return;
    }

    // Find the current row index
    const currentRowIndex = productRows.findIndex(row => row.id === rowId);

    if (lines.length === 1) {
      // Single value → paste into the current field only
      const cleanValue = lines[0];
      console.log('Single value paste:', cleanValue, 'into field:', field);

      if (field === 'unitPrice') {
        // Clean price value - remove currency symbols and keep only numbers and decimal
        const priceValue = cleanValue.replace(/[^\d.]/g, '');
        handleProductChange(rowId, field, priceValue);
      } else if (field === 'quantity') {
        // Clean quantity value - keep only numbers
        const qtyValue = cleanValue.replace(/[^\d]/g, '');
        handleProductChange(rowId, field, qtyValue || '1');
      } else {
        // For name or other text fields, use as-is
        handleProductChange(rowId, field, cleanValue);
      }
    } else {
      // Multiple lines → create new rows, but keep each value in the SAME COLUMN
      console.log('Multi-line paste, creating new rows for field:', field);

      // Update the current row with the first line (in the correct field)
      const firstValue = lines[0];
      if (field === 'unitPrice') {
        const priceValue = firstValue.replace(/[^\d.]/g, '');
        handleProductChange(rowId, field, priceValue);
      } else if (field === 'quantity') {
        const qtyValue = firstValue.replace(/[^\d]/g, '');
        handleProductChange(rowId, field, qtyValue || '1');
      } else {
        handleProductChange(rowId, field, firstValue);
      }

      // Build new rows for the remaining lines - SAME COLUMN ONLY
      const newRows = lines.slice(1).map((line, index) => {
        const newRow = {
          id: `row-${Date.now()}-${index}`,
          name: '',
          unitPrice: '',
          quantity: '1',
          quantityUnit: 'pcs',
          photo: null as File | null,
          photoPreview: '',
          // Fixed the syntax error – initialise an empty object and cast to Record<string, string>
          customData: {} as Record<string, string>
        };

        // Initialise any custom column data
        customColumns.forEach(col => {
          newRow.customData[col.id] = '';
        });

        // Only fill the SAME field that was pasted into
        const cleanValue = line;
        if (field === 'name') {
          newRow.name = cleanValue;
        } else if (field === 'unitPrice') {
          newRow.unitPrice = cleanValue.replace(/[^\d.]/g, '');
        } else if (field === 'quantity') {
          newRow.quantity = cleanValue.replace(/[^\d]/g, '') || '1';
        } else if (field.startsWith('custom-')) {
          // Handle custom columns
          newRow.customData[field] = cleanValue;
        }

        return newRow;
      });

      // Insert the newly created rows after the current row
      setProductRows(prev => {
        const copy = [...prev];
        copy.splice(currentRowIndex + 1, 0, ...newRows);
        return copy;
      });

      // Inform the user
      setTimeout(() => {
        alert(
          `✅ Pasted ${lines.length} values into ${field} column and created ${newRows.length} new product rows!`
        );
      }, 100);
    }
  };

  // -------------------------------------------------------------------------
  // Enhanced Row‑paste handling (tab‑separated Excel row) - ONLY for product name field
  // -------------------------------------------------------------------------
  const handleRowPaste = (rowId: string, e: React.ClipboardEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const pastedText = e.clipboardData.getData('text');
    console.log('Row paste:', pastedText);

    if (!pastedText || !pastedText.trim()) return;

    const lines = pastedText
      .trim()
      .split(/[\r\n]+/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    console.log('Row paste lines:', lines);

    const processLine = (line: string, targetRowId: string) => {
      if (line.includes('\t')) {
        // Tab-separated data (Excel format)
        const cells = line.split('\t').map(cell => cell.trim());
        console.log('Processing tab-separated cells:', cells);

        if (cells[0]) {
          handleProductChange(targetRowId, 'name', cells[0]);
        }
        if (cells[1]) {
          const price = cells[1].replace(/[^\d.]/g, '');
          if (price) handleProductChange(targetRowId, 'unitPrice', price);
        }
        if (cells[2]) {
          const qty = cells[2].replace(/[^\d]/g, '');
          if (qty) handleProductChange(targetRowId, 'quantity', qty);
        }

        // Custom columns (if any)
        if (cells.length > 3 && customColumns.length > 0) {
          customColumns.forEach((col, idx) => {
            const cellIdx = 3 + idx;
            if (cells[cellIdx]) {
              handleCustomDataChange(
                targetRowId,
                col.id,
                cells[cellIdx]
              );
            }
          });
        }
      } else {
        // No tab – treat whole line as product name
        console.log('Processing single line as name:', line);
        handleProductChange(targetRowId, 'name', line);
      }
    };

    // First line → current row
    if (lines.length > 0) {
      processLine(lines[0], rowId);
    }

    // Remaining lines → new rows
    if (lines.length > 1) {
      const currentRowIndex = productRows.findIndex(row => row.id === rowId);

      const newRows = lines.slice(1).map((line, idx) => ({
        id: `row-${Date.now()}-${idx}`,
        name: '',
        unitPrice: '',
        quantity: '1',
        quantityUnit: 'pcs',
        photo: null as File | null,
        photoPreview: '',
        customData: customColumns.reduce((acc, col) => {
          acc[col.id] = '';
          return acc;
        }, {} as Record<string, string>)
      }));

      // Add new rows to state
      setProductRows(prev => {
        const copy = [...prev];
        copy.splice(currentRowIndex + 1, 0, ...newRows);
        return copy;
      });

      // Process remaining lines after state update
      setTimeout(() => {
        newRows.forEach((row, idx) => {
          processLine(lines[idx + 1], row.id);
        });
      }, 10);

      setTimeout(() => {
        alert(
          `✅ Pasted ${lines.length} product rows successfully!`
        );
      }, 100);
    }
  };

  // -------------------------------------------------------------------------
  // Excel paste modal state & helpers
  // -------------------------------------------------------------------------
  const [showPasteModal, setShowPasteModal] = useState(false);
  const [pasteData, setPasteData] = useState('');
  const [pastePreview, setPastePreview] = useState<string[][]>([]);
  const [columnMapping, setColumnMapping] = useState<Record<number, string>>({});
  const [importWithPhotos, setImportWithPhotos] = useState(false);
  const [photoUrls, setPhotoUrls] = useState<Record<number, string>>({});

  const handlePasteFromExcel = () => {
    setShowPasteModal(true);
    setPasteData('');
    setPastePreview([]);
    setColumnMapping({});
  };

  const processPasteData = (data: string) => {
    if (!data.trim()) {
      setPastePreview([]);
      return;
    }

    let rows: string[][] = [];

    // Handle different paste formats
    if (data.includes('\t')) {
      // Excel format - split by lines and then by tabs
      const lines = data.trim().split('\n');
      rows = lines.map(line => line.split('\t'));
    } else if (data.includes(',')) {
      // CSV format
      const lines = data.trim().split('\n');
      rows = lines.map(line => {
        // Simple CSV parsing
        return line.split(',').map(cell => cell.trim().replace(/^["']|["']$/g, ''));
      });
    } else {
      // Single column or space-separated data
      const lines = data.trim().split('\n');
      if (lines.length > 1) {
        rows = lines.map(line => [line.trim()]);
      } else {
        const items = data.trim().split(/\s+/);
        rows = items.length > 1 ? items.map(item => [item.trim()]) : [[data.trim()]];
      }
    }

    // Filter empty rows
    const filteredRows = rows.filter(row => row.some(cell => cell && cell.trim() !== ''));
    setPastePreview(filteredRows);

    // Auto‑detect column mapping
    if (filteredRows.length > 0) {
      const headerRow = filteredRows[0];
      const autoMapping: Record<number, string> = {};

      headerRow.forEach((header, index) => {
        const lowerHeader = header.toLowerCase().trim();

        // Auto‑detect common patterns
        if (lowerHeader.includes('product') || lowerHeader.includes('name') || lowerHeader.includes('item')) {
          autoMapping[index] = 'name';
        } else if (lowerHeader.includes('price') || lowerHeader.includes('cost') || lowerHeader.includes('unit')) {
          autoMapping[index] = 'unitPrice';
        } else if (lowerHeader.includes('quantity') || lowerHeader.includes('qty') || lowerHeader.includes('amount')) {
          autoMapping[index] = 'quantity';
        } else if (lowerHeader.includes('photo') || lowerHeader.includes('image') || lowerHeader.includes('url') || lowerHeader.includes('pic')) {
          autoMapping[index] = 'photo';
        } else if (lowerHeader.includes('sku') || lowerHeader.includes('code')) {
          // Map to first available custom column or create suggestion
          autoMapping[index] = 'sku';
        } else if (lowerHeader.includes('description') || lowerHeader.includes('desc')) {
          autoMapping[index] = 'description';
        } else if (lowerHeader.includes('category') || lowerHeader.includes('type')) {
          autoMapping[index] = 'category';
        } else if (lowerHeader.includes('brand') || lowerHeader.includes('manufacturer')) {
          autoMapping[index] = 'brand';
        }
      });

      // If no auto‑mapping and we have data, suggest first column as product name
      if (Object.keys(autoMapping).length === 0 && filteredRows.length > 0) {
        autoMapping[0] = 'name';
        if (filteredRows[0].length > 1) autoMapping[1] = 'unitPrice';
        if (filteredRows[0].length > 2) autoMapping[2] = 'quantity';
      }

      setColumnMapping(autoMapping);
    }
  };

  const downloadImageFromUrl = async (url: string): Promise<string> => {
    try {
      // For demo purposes, we'll create a placeholder image
      // In a real app, you'd implement proper image downloading
      return `https://readdy.ai/api/search-image?query=product%20${url.replace(/[^\\w]/g, ' ')}%20professional%20white%20background&width=300&height=200&seq=${Math.random()
        .toString(36)
        .substr(2, 9)}&orientation=landscape`;
    } catch (error) {
      console.error('Error downloading image:', error);
      return '';
    }
  };

  const applyPasteData = async () => {
    if (pastePreview.length === 0) return;

    // Determine if first row is header
    const hasHeader =
      Object.keys(columnMapping).length > 0 &&
      pastePreview.length > 1 &&
      pastePreview[0].some(cell =>
        cell.toLowerCase().includes('product') ||
        cell.toLowerCase().includes('name') ||
        cell.toLowerCase().includes('price') ||
        cell.toLowerCase().includes('photo')
      );

    const dataRows = hasHeader ? pastePreview.slice(1) : pastePreview;

    if (dataRows.length === 0) return;

    // Show processing indicator for photos
    const shouldProcessPhotos = importWithPhotos && Object.values(columnMapping).includes('photo');
    if (shouldProcessPhotos) {
      alert('Processing images... This may take a moment.');
    }

    // Create new product rows from paste data
    const newRows = await Promise.all(
      dataRows.map(async (row, index) => {
        const newRow: any = {
          id: `row-${Date.now()}-${index}`,
          name: '',
          unitPrice: '',
          quantity: '1',
          total: '$0.00',
          photo: null,
          photoPreview: '',
          customData: {} as Record<string, string>
        };

        // Initialise custom data for existing custom columns
        customColumns.forEach(col => {
          newRow.customData[col.id] = '';
        });

        // Map data based on column mapping
        for (const [colIndex, fieldName] of Object.entries(columnMapping)) {
          const cellValue = row[parseInt(colIndex)] || '';
          const trimmedValue = cellValue.trim();

          if (fieldName === 'name') {
            newRow.name = trimmedValue;
          } else if (fieldName === 'unitPrice') {
            // Clean price data
            const cleanPrice = trimmedValue.replace(/[^0-9.]/g, '');
            newRow.unitPrice = cleanPrice;
          } else if (fieldName === 'quantity') {
            // Clean quantity data
            const cleanQty = trimmedValue.replace(/[^0-9.]/g, '');
            newRow.quantity = cleanQty || '1';
          } else if (fieldName === 'photo' && importWithPhotos && trimmedValue) {
            // Handle photo URL
            try {
              if (trimmedValue.startsWith('http')) {
                // Direct URL - generate preview image
                newRow.photoPreview = await downloadImageFromUrl(trimmedValue);
              } else {
                // Text description - generate image from description
                newRow.photoPreview = `https://readdy.ai/api/search-image?query=${encodeURIComponent(
                  trimmedValue
                )}%20product%20professional%20white%20background&width=300&height=200&seq=${Math.random()
                  .toString(36)
                  .substr(2, 9)}&orientation=landscape`;
              }
            } catch (error) {
              console.error('Error processing photo:', error);
            }
          } else if (fieldName.startsWith('custom-')) {
            newRow.customData[fieldName] = trimmedValue;
          } else {
            // Other standard fields (sku, description, etc.)
            if (!newRow.customData) newRow.customData = {};
            newRow.customData[fieldName] = trimmedValue;
          }
        }

        // Calculate total if we have price and quantity
        if (newRow.unitPrice && newRow.quantity) {
          const price = parseFloat(newRow.unitPrice) || 0;
          const qty = parseInt(newRow.quantity) || 0;
          const total = price * qty;
          newRow.total = `$${total.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          })}`;
        }

        // If no mapping was set, use intelligent defaults
        if (Object.keys(columnMapping).length === 0 && row.length > 0) {
          newRow.name = row[0].trim();
          if (row.length > 1) {
            const priceValue = row[1].replace(/[^0-9.]/g, '');
            if (priceValue) newRow.unitPrice = priceValue;
          }
          if (row.length > 2) {
            const qtyValue = row[2].replace(/[^0-9.]/g, '');
            if (qtyValue) newRow.quantity = qtyValue;
          }
        }

        return newRow;
      })
    );

    // Ask user whether to replace or append
    const replace = confirm(
      `Found ${newRows.length} products to import.${shouldProcessPhotos ? '\\n(Photos will be processed)' : ''}\\n\\nClick OK to REPLACE existing products\\nClick Cancel to ADD to existing products`
    );

    if (replace) {
      setProductRows(newRows);
    } else {
      setProductRows(prev => [...prev, ...newRows]);
    }

    setShowPasteModal(false);
    setPasteData('');
    setPastePreview([]);
    setColumnMapping({});
    setImportWithPhotos(false);
    setPhotoUrls({});

    // Show success message
    alert(`Successfully imported ${newRows.length} products!${shouldProcessPhotos ? ' Photos are being processed.' : ''}`);
  };

  // -------------------------------------------------------------------------
  // Input handlers (form, products, custom fields, photos, etc.)
  // -------------------------------------------------------------------------
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProductChange = (rowId: string, field: string, value: string) => {
    setProductRows(prev =>
      prev.map(row => (row.id === rowId ? { ...row, [field]: value } : row))
    );
  };

  const handleCustomDataChange = (
    rowId: string,
    columnId: string,
    value: string
  ) => {
    setProductRows(prev =>
      prev.map(row =>
        row.id === rowId
          ? { ...row, customData: { ...row.customData, [columnId]: value } }
          : row
      )
    );
  };

  const handlePhotoChange = (rowId: string, e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = ev => {
      setProductRows(prev =>
        prev.map(row =>
          row.id === rowId
            ? { ...row, photo: file, photoPreview: ev.target?.result as string }
            : row
        )
      );
    };
    reader.readAsDataURL(file);
  };

  // Photo paste (Ctrl+V) handling
  const handlePhotoPaste = (rowId: string, e: React.ClipboardEvent) => {
    e.preventDefault();
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const blob = item.getAsFile();
        if (!blob) continue;

        const reader = new FileReader();
        reader.onload = ev => {
          setProductRows(prev =>
            prev.map(row =>
              row.id === rowId
                ? { ...row, photo: blob, photoPreview: ev.target?.result as string }
                : row
            )
          );
        };
        reader.readAsDataURL(blob);
        break; // only first image
      }
    }
  };

  // Drag‑and‑drop support for photos
  const handlePhotoDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handlePhotoDrop = (rowId: string, e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith('image/')) return;

    const reader = new FileReader();
    reader.onload = ev => {
      setProductRows(prev =>
        prev.map(row =>
          row.id === rowId
            ? { ...row, photo: file, photoPreview: ev.target?.result as string }
            : row
        )
      );
    };
    reader.readAsDataURL(file);
  };

  const clearPhoto = (rowId: string) => {
    setProductRows(prev =>
      prev.map(row =>
        row.id === rowId ? { ...row, photo: null, photoPreview: '' } : row
      )
    );
  };

  const addNewProductRow = () => {
    const newRow = {
      id: `row-${Date.now()}`,
      name: '',
      unitPrice: '',
      quantity: '1',
      quantityUnit: 'pcs',
      photo: null as File | null,
      photoPreview: '',
      customData: {} as Record<string, string>
    };

    // Add empty values for any defined custom columns
    customColumns.forEach(col => {
      newRow.customData[col.id] = '';
    });

    setProductRows(prev => [...prev, newRow]);
  };

  const removeProductRow = (rowId: string) => {
    if (productRows.length > 1) {
      setProductRows(prev => prev.filter(row => row.id !== rowId));
    }
  };

  const addCustomColumn = () => {
    if (!newColumn.name.trim()) return;

    const column: CustomColumn = {
      id: `custom-${Date.now()}`,
      name: newColumn.name,
      type: newColumn.type,
      required: newColumn.required,
      options:
        newColumn.type === 'select'
          ? newColumn.options.filter(opt => opt.trim())
          : undefined
    };

    setCustomColumns(prev => [...prev, column]);

    // Initialise this column for all existing rows
    setProductRows(prev =>
      prev.map(row => ({
        ...row,
        customData: { ...row.customData, [column.id]: '' }
      }))
    );

    setShowColumnModal(false);
    setNewColumn({ name: '', type: 'text', required: false, options: [''] });
  };

  const removeCustomColumn = (columnId: string) => {
    setCustomColumns(prev => prev.filter(col => col.id !== columnId));
    setProductRows(prev =>
      prev.map(row => {
        const { [columnId]: _removed, ...rest } = row.customData;
        return { ...row, customData: rest };
      })
    );
  };

  const calculateTotal = () => {
    return productRows.reduce((sum, row) => {
      const price = parseFloat(row.unitPrice) || 0;
      const qty = parseInt(row.quantity) || 0;
      return sum + price * qty;
    }, 0);
  };

  const validateForm = () => {
    setSubmitError(null);

    if (!formData.supplier.trim()) {
      setSubmitError('Supplier is required');
      return false;
    }
    if (!formData.buyer.trim()) {
      setSubmitError('Buyer is required');
      return false;
    }

    const validProducts = productRows.filter(row => {
      const hasName = row.name.trim() !== '';
      const hasPrice = row.unitPrice !== '' && parseFloat(row.unitPrice) > 0;
      const hasQty = row.quantity !== '' && parseInt(row.quantity) > 0;
      return hasName && hasPrice && hasQty;
    });

    if (validProducts.length === 0) {
      setSubmitError(
        'At least one valid product is required (with name, price, and quantity)'
      );
      return false;
    }

    for (const product of validProducts) {
      for (const column of customColumns) {
        if (column.required) {
          const value = product.customData[column.id];
          if (!value || value.trim() === '') {
            setSubmitError(`${column.name} is required for all products`);
            return false;
          }
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      if (!validateForm()) {
        setIsSubmitting(false);
        return;
      }

      const validProducts = productRows.filter(row => {
        const hasName = row.name.trim() !== '';
        const hasPrice = row.unitPrice !== '' && parseFloat(row.unitPrice) > 0;
        const hasQty = row.quantity !== '' && parseInt(row.quantity) > 0;
        return hasName && hasPrice && hasQty;
      });

      const purchaseStore = PurchaseStore.getInstance();

      const purchaseData = {
        supplier: formData.supplier.trim(),
        buyer: formData.buyer.trim(),
        deliveryDate: formData.deliveryDate,
        status: formData.status,
        products: validProducts.map(p => ({
          name: p.name.trim(),
          unitPrice: p.unitPrice,
          quantity: p.quantity,
          photo: p.photoPreview || '',
          customData: p.customData || {}
        })),
        customColumns: customColumns
      };

      const newPurchase = purchaseStore.addPurchase(purchaseData);

      setShowSuccess(true);
      setTimeout(() => {
        router.push(`/purchase/${newPurchase.id}`);
      }, 1500);
    } catch (error) {
      console.error('Error creating purchase order:', error);
      setSubmitError('Failed to create purchase order. Please try again.');
      setIsSubmitting(false);
    }
  };

  const isFormValid = () => {
    const hasRequiredFields = formData.supplier.trim() && formData.buyer.trim();

    const hasValidProducts = productRows.some(
      row =>
        row.name.trim() &&
        row.unitPrice &&
        parseFloat(row.unitPrice) > 0 &&
        row.quantity &&
        parseInt(row.quantity) > 0
    );

    return !!hasRequiredFields && !!hasValidProducts;
  };

  // -------------------------------------------------------------------------
  // Render
  // -------------------------------------------------------------------------
  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main
        className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
      >
        <div className="max-w-6xl mx-auto">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              New Purchase Order
            </h2>
            <p className="text-gray-600">Create a new purchase order from suppliers</p>
          </div>

          {showSuccess && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <i className="ri-check-line w-5 h-5 flex items-center justify-center text-green-600 mr-3"></i>
                <span className="text-green-800 font-medium">
                  Purchase order created successfully!
                </span>
              </div>
            </div>
          )}

          {submitError && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <i className="ri-error-warning-line w-5 h-5 flex items-center justify-center text-red-600 mr-3"></i>
                <span className="text-red-800 font-medium">{submitError}</span>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Purchase Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Purchase Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Supplier *
                  </label>
                  <SupplierDropdown
                    value={formData.supplier}
                    onChange={value => setFormData(prev => ({ ...prev, supplier: value }))}
                    placeholder="Select supplier"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Buyer *
                  </label>
                  <input
                    type="text"
                    name="buyer"
                    required
                    placeholder="Enter buyer name"
                    value={formData.buyer}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expected Delivery Date
                  </label>
                  <input
                    type="date"
                    name="deliveryDate"
                    value={formData.deliveryDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                  >
                    <option value="Pending">Pending</option>
                    <option value="Approved">Approved</option>
                    <option value="Ordered">Ordered</option>
                    <option value="Received">Received</option>
                    <option value="Cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Products Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Products
                </h3>
                <div className="flex items-center space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowExcelImportModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-green-600 border border-green-600 rounded-lg hover:bg-green-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-file-excel-2-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Import from Excel</span>
                  </button>

                  <button
                    type="button"
                    onClick={handlePasteFromExcel}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-clipboard-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Paste from Excel</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => setShowColumnModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-column-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Add Custom Column</span>
                  </button>

                  <button
                    type="button"
                    onClick={addNewProductRow}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Add Product</span>
                  </button>
                </div>
              </div>

              {/* Enhanced Direct paste helper */}
              <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <i className="ri-clipboard-line w-5 h-5 flex items-center justify-center text-blue-600 mt-0.5"></i>
                  <div className="text-sm text-blue-700">
                    <p className="font-medium mb-2">✨ Multiple Import Options Available:</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="font-medium">📂 Excel File Import:</p>
                        <ul className="list-disc list-inside ml-2 space-y-1 text-xs">
                          <li>Upload .xlsx, .xls, .csv files</li>
                          <li>Advanced column mapping</li>
                          <li>Photo processing support</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium">📋 Copy & Paste:</p>
                        <ul className="list-disc list-inside ml-2 space-y-1 text-xs">
                          <li>Copy directly from Excel/Sheets</li>
                          <li>Automatic column detection</li>
                          <li>Tab‑separated data support</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium">🎯 Direct Field Paste:</p>
                        <ul className="list-disc list-inside ml-2 space-y-1 text-xs">
                          <li>Single values or multiple lines</li>
                          <li>Creates new rows automatically</li>
                          <li>Data cleaning included</li>
                        </ul>
                      </div>
                    </div>
                    <p className="mt-2 text-xs text-blue-600">Blue dots (●) indicate paste‑enabled fields</p>
                  </div>
                </div>
              </div>

              {/* Products table */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        #
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        Photo
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        Product Name <span className="text-blue-500">●</span>
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        Unit Price <span className="text-green-500">●</span>
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        Quantity <span className="text-purple-500">●</span>
                      </th>
                      {customColumns.map(col => (
                        <th
                          key={col.id}
                          className="px-4 py-3 text-xs font-semibold text-purple-700 uppercase tracking-wide border border-gray-300 text-left"
                        >
                          <div className="flex items-center justify-between">
                            <span>{col.name}</span>
                            <button
                              type="button"
                              onClick={() => removeCustomColumn(col.id)}
                              className="text-red-400 hover:text-red-600 cursor-pointer"
                            >
                              <i className="ri-close-line w-3 h-3 flex items-center justify-center"></i>
                            </button>
                          </div>
                        </th>
                      ))}
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        Total
                      </th>
                      <th className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wide border border-gray-300 text-left">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {productRows.map((row, idx) => (
                      <tr key={row.id} className="bg-white border-b border-gray-200">
                        <td className="px-4 py-4 border border-gray-200 text-center">
                          <span className="text-sm font-semibold text-gray-600">
                            {idx + 1}
                          </span>
                        </td>

                        <td className="px-4 py-4 border border-gray-200">
                          <div className="flex flex-col items-center">
                            <input
                              type="file"
                              accept="image/*"
                              onChange={e => handlePhotoChange(row.id, e)}
                              className="hidden"
                              id={`photo-${row.id}`}
                            />
                            <div
                              ref={el => (pasteAreaRefs.current[row.id] = el)}
                              tabIndex={0}
                              onPaste={e => handlePhotoPaste(row.id, e)}
                              onDragOver={handlePhotoDragOver}
                              onDrop={e => handlePhotoDrop(row.id, e)}
                              onClick={() =>
                                document
                                  .getElementById(`photo-${row.id}`)
                                  ?.click()
                              }
                              className="relative w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors group focus:outline-none focus:ring-2 focus:ring-blue-500"
                              title="Click to upload, paste with Ctrl+V, or drag and drop"
                            >
                              {row.photoPreview ? (
                                <>
                                  <img
                                    src={row.photoPreview}
                                    alt="Product"
                                    className="w-full h-full object-cover rounded-lg"
                                  />
                                  <button
                                    type="button"
                                    onClick={e => {
                                      e.stopPropagation();
                                      clearPhoto(row.id);
                                    }}
                                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                                    title="Remove photo"
                                  >
                                    <i className="ri-close-line w-3 h-3 flex items-center justify-center"></i>
                                  </button>
                                </>
                              ) : (
                                <div className="text-center">
                                  <i className="ri-image-line w-6 h-6 flex items-center justify-center text-gray-400 mx-auto mb-1"></i>
                                  <p className="text-xs text-gray-500">Click or paste</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>

                        {/* Enhanced Product name row - supports row paste and direct paste */}
                        <td className="px-4 py-4 border border-gray-200">
                          <div
                            className="relative"
                            onPaste={e => handleRowPaste(row.id, e)}
                            title="Paste Excel rows here (tab-separated) or single product names"
                          >
                            <input
                              type="text"
                              required
                              placeholder="Enter product name"
                              value={row.name}
                              onChange={e =>
                                handleProductChange(row.id, 'name', e.target.value)
                              }
                              onPaste={e => handleDirectPaste(row.id, 'name', e)}
                              className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                            />
                            <div className="absolute -top-1 -right-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full shadow-sm" title="Direct paste enabled - Product names"></div>
                            </div>
                          </div>
                        </td>

                        {/* Enhanced Unit price - supports direct paste */}
                        <td className="px-4 py-4 border border-gray-200">
                          <div className="relative">
                            <input
                              type="number"
                              required
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              value={row.unitPrice}
                              onChange={e =>
                                handleProductChange(row.id, 'unitPrice', e.target.value)
                              }
                              onPaste={e => handleDirectPaste(row.id, 'unitPrice', e)}
                              className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                            />
                            <div className="absolute -top-1 -right-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm" title="Direct paste enabled - Unit prices only"></div>
                            </div>
                          </div>
                        </td>

                        {/* Enhanced Quantity - supports direct paste */}
                        <td className="px-4 py-4 border border-gray-200">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 relative">
                              <input
                                type="number"
                                required
                                min="1"
                                placeholder="1"
                                value={row.quantity}
                                onChange={e =>
                                  handleProductChange(row.id, 'quantity', e.target.value)
                                }
                                onPaste={e => handleDirectPaste(row.id, 'quantity', e)}
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                              <div className="absolute -top-1 -right-1">
                                <div className="w-2 h-2 bg-purple-500 rounded-full shadow-sm" title="Direct paste enabled - Quantities only"></div>
                              </div>
                            </div>
                            <span className="text-xs text-gray-500">{row.quantityUnit}</span>
                          </div>
                        </td>

                        {/* Custom columns with paste support */}
                        {customColumns.map(col => (
                          <td key={col.id} className="px-4 py-4 border border-gray-200">
                            {col.type === 'select' ? (
                              <select
                                value={row.customData[col.id] || ''}
                                onChange={e =>
                                  handleCustomDataChange(row.id, col.id, e.target.value)
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm pr-8"
                              >
                                <option value="">Select option</option>
                                {col.options?.map((opt, i) => (
                                  <option key={i} value={opt}>
                                    {opt}
                                  </option>
                                ))}
                              </select>
                            ) : (
                              <div className="relative">
                                <input
                                  type={col.type}
                                  placeholder={`Enter ${col.name.toLowerCase()}`}
                                  value={row.customData[col.id] || ''}
                                  onChange={e =>
                                    handleCustomDataChange(row.id, col.id, e.target.value)
                                  }
                                  onPaste={e => handleDirectPaste(row.id, col.id, e)}
                                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                />
                                <div className="absolute -top-1 -right-1">
                                  <div className="w-2 h-2 bg-orange-500 rounded-full shadow-sm" title={`Direct paste enabled - ${col.name} only`}></div>
                                </div>
                              </div>
                            )}
                          </td>
                        ))}

                        {/* Row total */}
                        <td className="px-4 py-4 border border-gray-200 text-right">
                          <span className="text-sm font-semibold text-green-600">
                            ${(
                              (parseFloat(row.unitPrice) || 0) *
                              (parseInt(row.quantity) || 0)
                            ).toFixed(2)}
                          </span>
                        </td>

                        {/* Actions */}
                        <td className="px-4 py-4 border border-gray-200 text-center">
                          {productRows.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeProductRow(row.id)}
                              className="px-3 py-1.5 text-xs font-medium text-red-600 bg-white border border-red-200 rounded hover:bg-red-50 cursor-pointer"
                              title="Delete this product row"
                            >
                              <i className="ri-delete-bin-line w-3 h-3 flex items-center justify-center"></i>
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Total summary */}
              <div className="mt-6 flex justify-end">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      Total Amount
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      ${calculateTotal().toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit / Cancel */}
            <div className="flex items-center justify-end space-x-4">
              <Link
                href="/purchase"
                className="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
              >
                Cancel
              </Link>

              <button
                type="submit"
                disabled={isSubmitting || !isFormValid()}
                className="px-6 py-3 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Create Purchase Order</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </main>

      {/* Add Column Modal */}
      {showColumnModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Add Custom Column
              </h3>
              <button
                type="button"
                onClick={() => setShowColumnModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Column Name
                </label>
                <input
                  type="text"
                  required
                  value={newColumn.name}
                  onChange={e =>
                    setNewColumn(prev => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="Enter column name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data Type
                </label>
                <select
                  value={newColumn.type}
                  onChange={e =>
                    setNewColumn(prev => ({
                      ...prev,
                      type: e.target.value as any
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                >
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="date">Date</option>
                  <option value="select">Dropdown</option>
                </select>
              </div>

              {newColumn.type === 'select' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Options
                  </label>
                  <div className="space-y-2">
                    {newColumn.options.map((opt, i) => (
                      <div key={i} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={opt}
                          onChange={e => {
                            const newOpts = [...newColumn.options];
                            newOpts[i] = e.target.value;
                            setNewColumn(prev => ({
                              ...prev,
                              options: newOpts
                            }));
                          }}
                          placeholder={'Option ' + (i + 1)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                        {newColumn.options.length > 1 && (
                          <button
                            type="button"
                            onClick={() => {
                              const newOpts = newColumn.options.filter(
                                (_, idx) => idx !== i
                              );
                              setNewColumn(prev => ({
                                ...prev,
                                options: newOpts
                              }));
                            }}
                            className="p-2 text-red-500 hover:text-red-700 cursor-pointer"
                          >
                            <i className="ri-close-line w-4 h-4 flex items-center justify-center"></i>
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() =>
                        setNewColumn(prev => ({
                          ...prev,
                          options: [...prev.options, '']
                        }))
                      }
                      className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-800 cursor-pointer"
                    >
                      <i className="ri-add-line w-3 h-3 flex items-center justify-center"></i>
                      <span>Add Option</span>
                    </button>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowColumnModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={addCustomColumn}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer"
                >
                  Add Column
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Excel Import Modal */}
      <ExcelImportModal
        isOpen={showExcelImportModal}
        onClose={() => setShowExcelImportModal(false)}
        onImport={handleExcelImport}
        customColumns={customColumns}
      />

      {/* Enhanced Paste Modal */}
      {showPasteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-6xl max-h-[95vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Import Products from Excel/CSV</h3>
              <button
                onClick={() => setShowPasteModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-6">
              {/* Instructions */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <i className="ri-information-line w-5 h-5 flex items-center justify-center text-blue-600 mt-0.5"></i>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">📋 How to Import:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                      <div>
                        <p className="font-medium mb-1">🎯 From Excel:</p>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          <li>Select and copy your Excel data (Ctrl+C)</li>
                          <li>Include headers for auto-mapping</li>
                          <li>Paste below and map columns</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium mb-1">📸 Photo Support:</p>
                        <ul className="list-disc list-inside space-y-1 text-xs">
                          <li>Include photo URLs in data</li>
                          <li>Or use product descriptions</li>
                          <li>Enable photo processing below</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Paste Area */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Paste your Excel/CSV data here:
                </label>
                <textarea
                  value={pasteData}
                  onChange={e => {
                    setPasteData(e.target.value);
                    processPasteData(e.target.value);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={8}
                  placeholder={`Product Name\tUnit Price\tQuantity\tPhoto URL
Wireless Mouse\t29.99\t10\thttps://example.com/mouse.jpg
USB Cable\t12.50\t25\tprofessional USB cable product
Monitor Stand\t89.99\t5\taluminum monitor stand office`}
                />
              </div>

              {/* Photo Processing Option */}
              <div className="flex items-center space-x-3 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <input
                  type="checkbox"
                  id="importWithPhotos"
                  checked={importWithPhotos}
                  onChange={e => setImportWithPhotos(e.target.checked)}
                  className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                />
                <label htmlFor="importWithPhotos" className="text-sm font-medium text-purple-900">
                  📸 Process photos during import (URLs or generate from descriptions)
                </label>
              </div>

              {pastePreview.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">
                    📊 Preview & Column Mapping ({pastePreview.length} rows detected)
                  </h4>

                  {/* Column Mapping */}
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Map Excel columns to product fields:</h5>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {pastePreview[0]?.map((header, index) => (
                        <div key={index} className="flex flex-col">
                          <label className="text-xs text-gray-600 mb-1">
                            Column {index + 1}: "{header.substring(0, 15)}{header.length > 15 ? '...' : ''}"
                          </label>
                          <select
                            value={columnMapping[index] || ''}
                            onChange={e => setColumnMapping({ ...columnMapping, [index]: e.target.value })}
                            className="text-xs border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500 pr-6"
                          >
                            <option value="">Skip Column</option>
                            <option value="name">Product Name</option>
                            <option value="unitPrice">Unit Price</option>
                            <option value="quantity">Quantity</option>
                            <option value="photo">Photo URL/Description</option>
                            <option value="sku">SKU/Code</option>
                            <option value="description">Description</option>
                            <option value="category">Category</option>
                            <option value="brand">Brand</option>
                            {customColumns.map(col => (
                              <option key={col.id} value={col.id}>{col.name}</option>
                            ))}
                          </select>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Preview Table */}
                  <div className="border border-gray-300 rounded-lg overflow-hidden">
                    <div className="overflow-x-auto max-h-60">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            {pastePreview[0]?.map((header, index) => (
                              <th key={index} className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div className="flex flex-col">
                                  <span>Col {index + 1}</span>
                                  <span className="text-blue-600 font-semibold">
                                    {columnMapping[index] ? (columnMapping[index] === 'name' ? 'Name' : columnMapping[index] === 'unitPrice' ? 'Price' : columnMapping[index] === 'quantity' ? 'Qty' : columnMapping[index]) : 'Skip'}
                                  </span>
                                </div>
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {pastePreview.slice(0, 10).map((row, rowIndex) => (
                            <tr key={rowIndex} className={rowIndex === 0 ? 'bg-yellow-50' : ''}>
                              {row.map((cell, cellIndex) => (
                                <td key={cellIndex} className="px-3 py-2 text-xs text-gray-900 max-w-32">
                                  <div className="truncate" title={cell}>
                                    {columnMapping[cellIndex] === 'photo' && cell ? (
                                      <div className="flex items-center space-x-1">
                                        <i className="ri-image-line w-3 h-3 flex items-center justify-center text-purple-600"></i>
                                        <span className="text-purple-600">Photo</span>
                                      </div>
                                    ) : (
                                      cell
                                    )}
                                  </div>

                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {pastePreview.length > 10 && (
                    <p className="text-xs text-gray-500 mt-2">... and {pastePreview.length - 10} more rows</p>
                  )}

                  {/* Mapping Summary */}
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <h5 className="text-sm font-medium text-green-800 mb-2">📋 Import Summary:</h5>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div className="text-green-700">
                        <span className="font-medium">Products:</span> {pastePreview.length - (pastePreview[0]?.some(cell => cell.toLowerCase().includes('product') || cell.toLowerCase().includes('name')) ? 1 : 0)}
                      </div>
                      <div className="text-green-700">
                        <span className="font-medium">Columns:</span> {Object.keys(columnMapping).length}
                      </div>
                      <div className="text-green-700">
                        <span className="font-medium">Photos:</span> {Object.values(columnMapping).includes('photo') ? 'Yes' : 'No'}
                      </div>
                      <div className="text-green-700">
                        <span className="font-medium">Action:</span> Replace/Append
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowPasteModal(false)}
                className="px-4 py-2 text-gray-700 hover:text-gray-900 cursor-pointer whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={applyPasteData}
                disabled={pastePreview.length === 0}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
              >
                <i className="ri-download-line w-4 h-4 flex items-center justify-center"></i>
                <span>Import {pastePreview.length} Products</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
