'use client';

import Header from '@/components/Header';
import PurchaseTable from '@/components/PurchaseTable';
import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function PurchasePage() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');

  // Load saved navigation mode from localStorage on component mount
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
      <main className={`transition-all duration-300 p-6 ${
        navMode === 'topnav' ? '' : (sidebarOpen ? 'pl-64' : 'pl-16')
      }`}>
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Purchase Orders</h1>
                <p className="text-gray-600">Manage purchase orders and supplier relationships</p>
              </div>
              <Link
                href="/purchase/new"
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
              >
                <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                <span>New Purchase Order</span>
              </Link>
            </div>
          </div>
          
          <PurchaseTable />
        </div>
      </main>
    </div>
  );
}