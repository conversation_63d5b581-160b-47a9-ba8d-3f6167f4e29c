'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import SalesStore, { Sale, Product, CustomColumn } from '@/lib/salesStore';

interface SalesDetailClientProps {
  saleId: string;
}

export default function SalesDetailClient({ saleId }: SalesDetailClientProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sale, setSale] = useState<Sale | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingProduct, setEditingProduct] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // States for custom columns & export
  const [showColumnModal, setShowColumnModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [newColumn, setNewColumn] = useState({
    name: '',
    type: 'text' as const,
    required: false,
    options: ['']
  });
  const [exportFormat, setExportFormat] = useState<'excel' | 'html'>('html');
  const [selectedExportColumns, setSelectedExportColumns] = useState([
    'orderInfo',
    'customer',
    'salesRep',
    'salesDate',
    'products',
    'total',
    'status'
  ]);

  // New Product States
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    sku: '',
    unitPrice: '',
    quantity: 1,
    photo: null as File | null,
    photoPreview: '',
    customData: {} as Record<string, string>
  });

  // Payment System States
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showCreateInvoiceModal, setShowCreateInvoiceModal] = useState(false);
  const [showRecordPaymentModal, setShowRecordPaymentModal] = useState(false);
  const [paymentData, setPaymentData] = useState({
    amount: '',
    method: 'Bank Transfer' as any,
    notes: '',
    dueDate: ''
  });
  const [recordPaymentData, setRecordPaymentData] = useState({
    paymentId: '',
    amount: '',
    method: 'Cash' as any,
    notes: ''
  });
  const [salePayments, setSalePayments] = useState<any[]>([]);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // WeChat Integration States
  const [showWeChatModal, setShowWeChatModal] = useState(false);
  const [wechatConfig, setWechatConfig] = useState({
    recipientOpenId: '',
    recipientName: '',
    message: '',
    sendType: 'template' as 'template' | 'direct'
  });
  const [isSendingWeChat, setIsSendingWeChat] = useState(false);
  const [wechatSendResult, setWechatSendResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // WhatsApp Integration States
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const [whatsappConfig, setWhatsappConfig] = useState({
    phoneNumber: '',
    recipientName: '',
    message: '',
    sendType: 'template' as 'template' | 'direct'
  });
  const [isSendingWhatsApp, setIsSendingWhatsApp] = useState(false);
  const [whatsappSendResult, setWhatsappSendResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const [editForm, setEditForm] = useState({
    customer: '',
    salesRep: '',
    deliveryDate: '',
    status: ''
  });

  const [productEditForm, setProductEditForm] = useState<Product>({
    name: '',
    sku: '',
    unitPrice: '',
    quantity: 0,
    total: '',
    photo: '',
    customData: {}
  });

  // -------------------------------------------------------------------------
  // Initialise / subscribe to the sales data
  // -------------------------------------------------------------------------
  useEffect(() => {
    const salesStore = SalesStore.getInstance();

    const loadSale = () => {
      const saleData = salesStore.getSaleById(saleId);
      if (saleData) {
        setSale(saleData);
        setEditForm({
          customer: saleData.customer,
          salesRep: saleData.salesRep || saleData.vendor,
          deliveryDate: saleData.deliveryDate || '',
          status: saleData.status
        });
        
        // Load payments for this sale
        const payments = salesStore.getPaymentsByCustomer(saleData.customer);
        const saleSpecificPayments = payments.filter(p => p.orderId === saleId);
        setSalePayments(saleSpecificPayments);
      }
    };

    loadSale();
    setIsLoading(false);

    const unsubscribe = salesStore.subscribe(() => {
      loadSale();
    });

    return unsubscribe;
  }, [saleId]);

  // -------------------------------------------------------------------------
  // Payment System Functions
  // -------------------------------------------------------------------------
  const handleCreateInvoice = async () => {
    if (!sale || !paymentData.dueDate) return;

    setIsProcessingPayment(true);
    try {
      const salesStore = SalesStore.getInstance();
      const newPayment = salesStore.createInvoiceFromSale(saleId, paymentData.dueDate);
      
      // Update local state
      setSalePayments(prev => [newPayment, ...prev]);
      
      // Reset form
      setPaymentData({
        amount: '',
        method: 'Bank Transfer',
        notes: '',
        dueDate: ''
      });
      
      setShowCreateInvoiceModal(false);
      alert('Invoice created successfully!');
    } catch (error) {
      console.error('Error creating invoice:', error);
      alert('Error creating invoice. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handleRecordPayment = async () => {
    if (!recordPaymentData.paymentId || !recordPaymentData.amount) return;

    setIsProcessingPayment(true);
    try {
      const salesStore = SalesStore.getInstance();
      const amount = parseFloat(recordPaymentData.amount);
      
      if (isNaN(amount) || amount <= 0) {
        alert('Please enter a valid payment amount');
        return;
      }

      salesStore.recordPayment(
        recordPaymentData.paymentId,
        amount,
        recordPaymentData.method,
        recordPaymentData.notes
      );

      // Update local state
      const updatedPayments = salesStore.getPaymentsByCustomer(sale?.customer || '');
      const saleSpecificPayments = updatedPayments.filter(p => p.orderId === saleId);
      setSalePayments(saleSpecificPayments);

      // Reset form
      setRecordPaymentData({
        paymentId: '',
        amount: '',
        method: 'Cash',
        notes: ''
      });

      setShowRecordPaymentModal(false);
      alert('Payment recorded successfully!');
    } catch (error) {
      console.error('Error recording payment:', error);
      alert('Error recording payment. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Partial':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Pending':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const calculatePaymentSummary = () => {
    const totalAmount = parseFloat(sale?.total.replace(/[$,]/g, '') || '0');
    const totalPaid = salePayments.reduce((sum, payment) => sum + payment.paidAmount, 0);
    const totalOutstanding = salePayments.reduce((sum, payment) => sum + payment.remainingAmount, 0);
    
    return {
      totalAmount,
      totalPaid,
      totalOutstanding,
      paymentProgress: totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0
    };
  };

  // -------------------------------------------------------------------------
  // Add New Product functionality
  // -------------------------------------------------------------------------
  const handleAddProduct = () => {
    if (!sale || !newProduct.name.trim() || !newProduct.unitPrice) return;

    const unitPrice = parseFloat(newProduct.unitPrice) || 0;
    const quantity = newProduct.quantity || 1;
    const total = unitPrice * quantity;

    const productToAdd: Product = {
      name: newProduct.name,
      sku:
        newProduct.sku ||
        `SKU-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      unitPrice: `$${unitPrice.toFixed(2)}`,
      quantity: quantity,
      total: `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      photo: newProduct.photoPreview,
      customData: newProduct.customData
    };

    try {
      const salesStore = SalesStore.getInstance();

      // Add the new product to the sale
      const updatedProducts = [...sale.products, productToAdd];

      // Recalculate the sale total
      const newSaleTotal = updatedProducts.reduce((sum, prod) => {
        return sum + parseFloat(prod.total.replace(/[$,]/g, ''));
      }, 0);

      // Update the sale with new product and recalculated totals
      salesStore.updateSale(saleId, {
        products: updatedProducts,
        total: `$${newSaleTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        commission: `$${(newSaleTotal * 0.1).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
      });

      // Reset form and close modal
      setNewProduct({
        name: '',
        sku: '',
        unitPrice: '',
        quantity: 1,
        photo: null,
        photoPreview: '',
        customData: {}
      });
      setShowAddProductModal(false);
    } catch (error) {
      console.error('Failed to add product:', error);
    }
  };

  const handleNewProductPhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewProduct(prev => ({
          ...prev,
          photo: file,
          photoPreview: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removeNewProductPhoto = () => {
    setNewProduct(prev => ({
      ...prev,
      photo: null,
      photoPreview: ''
    }));
  };

  const handleNewProductCustomDataChange = (columnId: string, value: string) => {
    setNewProduct(prev => ({
      ...prev,
      customData: { ...prev.customData, [columnId]: value }
    }));
  };

  // -------------------------------------------------------------------------
  // Custom column handling
  // -------------------------------------------------------------------------
  const addCustomColumn = () => {
    if (!newColumn.name.trim() || !sale) return;

    const column: CustomColumn = {
      id: `custom-${Date.now()}`,
      name: newColumn.name,
      type: newColumn.type,
      required: newColumn.required,
      options:
        newColumn.type === 'select'
          ? newColumn.options.filter(opt => opt.trim())
          : undefined
    };

    try {
      const salesStore = SalesStore.getInstance();
      salesStore.addCustomColumn(saleId, column);
    } catch (error) {
      console.error('Failed to add custom column:', error);
    }

    setShowColumnModal(false);
    setNewColumn({ name: '', type: 'text', required: false, options: [''] });
  };

  const removeCustomColumn = (columnId: string) => {
    try {
      const salesStore = SalesStore.getInstance();
      salesStore.removeCustomColumn(saleId, columnId);
    } catch (error) {
      console.error('Failed to remove custom column:', error);
    }
  };

  const addSelectOption = () => {
    setNewColumn(prev => ({
      ...prev,
      options: [...prev.options, '']
    }));
  };

  const updateSelectOption = (index: number, value: string) => {
    setNewColumn(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => (i === index ? value : opt))
    }));
  };

  const removeSelectOption = (index: number) => {
    if (newColumn.options.length > 1) {
      setNewColumn(prev => ({
        ...prev,
        options: prev.options.filter((_, i) => i !== index)
      }));
    }
  };

  // -------------------------------------------------------------------------
  // Sale editing
  // -------------------------------------------------------------------------
  const handleEditSale = () => {
    if (isEditing && sale) {
      try {
        const salesStore = SalesStore.getInstance();
        salesStore.updateSale(saleId, {
          customer: editForm.customer,
          salesRep: editForm.salesRep,
          deliveryDate: editForm.deliveryDate,
          status: editForm.status,
          vendor: editForm.salesRep // keep vendor in sync
        });
      } catch (error) {
        console.error('Failed to update sale:', error);
      }
    }
    setIsEditing(!isEditing);
  };

  // -------------------------------------------------------------------------
  // Product editing
  // -------------------------------------------------------------------------
  const handleEditProduct = (productIndex: number) => {
    if (!sale) return;

    if (editingProduct === `product-${productIndex}`) {
      // Save changes
      try {
        const salesStore = SalesStore.getInstance();
        const updatedProduct = {
          ...productEditForm,
          sku:
            productEditForm.sku ||
            `SKU-${Math.random()
              .toString(36)
              .substr(2, 6)
              .toUpperCase()}`
        };
        salesStore.updateProduct(saleId, productIndex, updatedProduct);
      } catch (error) {
        console.error('Failed to update product:', error);
      }
      setEditingProduct(null);
    } else {
      // Start editing
      const p = sale.products[productIndex];
      setProductEditForm({
        ...p,
        sku:
          p.sku ||
          `SKU-${Math.random()
            .toString(36)
            .substr(2, 6)
            .toUpperCase()}`
      });
      setEditingProduct(`product-${productIndex}`);
    }
  };

  // -------------------------------------------------------------------------
  // Photo handling
  // -------------------------------------------------------------------------
  const handlePhotoUpload = (
    productIndex: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = ev => {
      const url = ev.target?.result as string;
      try {
        const salesStore = SalesStore.getInstance();
        salesStore.updateProduct(saleId, productIndex, { photo: url });
      } catch (error) {
        console.error('Failed to upload photo:', error);
      }
    };
    reader.readAsDataURL(file);
  };

  // -------------------------------------------------------------------------
  // Helpers
  // -------------------------------------------------------------------------
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Delivered':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // -------------------------------------------------------------------------
  // Export handling
  // -------------------------------------------------------------------------
  const getAvailableExportColumns = () => {
    const baseColumns = [
      { id: 'orderInfo', name: 'Order Information' },
      { id: 'customer', name: 'Customer' },
      { id: 'salesRep', name: 'Sales Representative' },
      { id: 'salesDate', name: 'Sales Date' },
      { id: 'deliveryDate', name: 'Delivery Date' },
      { id: 'total', name: 'Total Amount' },
      { id: 'commission', name: 'Commission' },
      { id: 'profit', name: 'Profit' },
      { id: 'status', name: 'Status' },
      { id: 'products', name: 'Products List' },
      { id: 'productDetails', name: 'Detailed Products' },
      { id: 'financialSummary', name: 'Financial Summary' }
    ];

    if (sale?.customColumns) {
      const customCols = sale.customColumns.map(col => ({
        id: `custom-${col.id}`,
        name: `Custom: ${col.name}`
      }));
      return [...baseColumns, ...customCols];
    }

    return baseColumns;
  };

  const generateOrderExport = () => {
    if (!sale) return {};

    const calculateProfit = () => {
      const totalAmount = parseFloat(sale.total.replace(/[$,]/g, ''));
      const commissionAmount = parseFloat(sale.commission.replace(/[$,]/g, ''));
      return totalAmount - commissionAmount;
    };

    return {
      orderInfo: {
        id: sale.id,
        orderDate: sale.salesDate,
        generatedDate: new Date().toLocaleDateString(),
        generatedTime: new Date().toLocaleTimeString()
      },
      customer: sale.customer,
      salesRep: sale.salesRep || sale.vendor,
      salesDate: sale.salesDate,
      deliveryDate: sale.deliveryDate || 'Not specified',
      total: sale.total,
      commission: sale.commission,
      profit: `$${calculateProfit().toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      status: sale.status,
      products: sale.products,
      customColumns: sale.customColumns || [],
      financialSummary: {
        subtotal: sale.total,
        commission: sale.commission,
        profit: `$${calculateProfit().toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        productCount: sale.products.length
      }
    };
  };

  const downloadOrderHTML = (data: any) => {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Order ${sale?.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .report { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 40px; text-align: center; }
        .header h1 { margin: 0; font-size: 32px; font-weight: 300; }
        .section { padding: 30px; border-bottom: 1px solid #e9ecef; }
        .section-title { color: #495057; font-size: 20px; font-weight: 600; margin-bottom: 20px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .info-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; }
        .info-card h4 { margin: 0 0 10px 0; color: #495057; font-size: 14px; font-weight: 600; }
        .info-card p { margin: 0; color: #6c757d; font-size: 16px; font-weight: 500; }
    </style>
</head>
<body>
    <div class="container">
        <div class="report">
            <div class="header">
                <h1>Sales Order Report</h1>
                <div>Order ID: ${data.orderInfo.id}</div>
            </div>
            <div class="section">
                <div class="section-title">Order Information</div>
                <div class="info-grid">
                    <div class="info-card">
                        <h4>Customer</h4>
                        <p>${data.customer}</p>
                    </div>
                    <div class="info-card">
                        <h4>Sales Rep</h4>
                        <p>${data.salesRep}</p>
                    </div>
                    <div class="info-card">
                        <h4>Total Amount</h4>
                        <p>${data.total}</p>
                    </div>
                    <div class="info-card">
                        <h4>Status</h4>
                        <p>${data.status}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-order-${sale?.id}-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const downloadOrderExcel = (data: any) => {
    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Sales Order Export - ${data.orderInfo.id}</h1>
    <p><strong>Customer:</strong> ${data.customer}</p>
    <p><strong>Total:</strong> ${data.total}</p>
    <p><strong>Status:</strong> ${data.status}</p>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-order-${sale?.id}-${new Date().toISOString().split('T')[0]}.xls`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleExport = () => {
    const data = generateOrderExport();
    if (exportFormat === 'excel') {
      downloadOrderExcel(data);
    } else {
      downloadOrderHTML(data);
    }
    setShowExportModal(false);
  };

  // -------------------------------------------------------------------------
  // Rendering
  // -------------------------------------------------------------------------
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <main
          className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
        >
          <div className="max-w-7xl mx-auto animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4" />
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8" />
            <div className="space-y-4">
              <div className="h-32 bg-gray-200 rounded" />
              <div className="h-48 bg-gray-200 rounded" />
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!sale) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        <main
          className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
        >
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-16">
              <i className="ri-file-search-line w-16 h-16 flex items-center justify-center mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Sales record not found
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                The sales record you're looking for doesn't exist.
              </p>
              <Link
                href="/sales"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
              >
                Back to Sales
              </Link>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const paymentSummary = calculatePaymentSummary();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      <main
        className={`transition-all duration-300 p-6 ${sidebarOpen ? 'pl-64' : 'pl-16'}`}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header section */}
          <div className="mb-6">
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/sales" className="hover:text-gray-700">
                Sales
              </Link>
              <i className="ri-arrow-right-s-line w-4 h-4 flex items-center justify-center" />
              <span className="text-gray-900 font-medium">{sale.id}</span>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Sales Order {sale.id}
                </h1>
                <p className="text-gray-600">
                  View and manage sales order details
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative group">
                  <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                    <i className="ri-download-line w-4 h-4 flex items-center justify-center" />
                    <span>Export Order</span>
                    <i className="ri-arrow-down-s-line w-3 h-3 flex items-center justify-center" />
                  </button>
                  
                  {/* Export Dropdown */}
                  <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 min-w-[200px] py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                      Export Options
                    </div>
                    
                    <button
                      onClick={() => setShowExportModal(true)}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 cursor-pointer"
                    >
                      <i className="ri-file-text-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Custom Export</div>
                        <div className="text-xs text-gray-500">Choose format and columns</div>
                      </div>
                    </button>

                    <button
                      onClick={() => {
                        const data = generateOrderExport();
                        downloadOrderHTML(data);
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 cursor-pointer"
                    >
                      <i className="ri-file-download-line w-4 h-4 flex items-center justify-center text-green-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Quick HTML Export</div>
                        <div className="text-xs text-gray-500">Professional report with all data</div>
                      </div>
                    </button>

                    <button
                      onClick={() => {
                        const data = generateOrderExport();
                        downloadOrderExcel(data);
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 cursor-pointer"
                    >
                      <i className="ri-file-excel-2-line w-4 h-4 flex items-center justify-center text-purple-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Quick Excel Export</div>
                        <div className="text-xs text-gray-500">Spreadsheet with all data</div>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Payment Actions Dropdown */}
                <div className="relative group">
                  <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100">
                    <i className="ri-money-dollar-circle-line w-4 h-4 flex items-center justify-center" />
                    <span>Payment</span>
                    <i className="ri-arrow-down-s-line w-3 h-3 flex items-center justify-center" />
                  </button>
                  
                  <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 min-w-[200px] py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                      Payment Options
                    </div>
                    
                    <button
                      onClick={() => setShowCreateInvoiceModal(true)}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 cursor-pointer"
                    >
                      <i className="ri-file-add-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                      <div className="text-left">
                        <div className="font-medium">Create Invoice</div>
                        <div className="text-xs text-gray-500">Generate payment invoice</div>
                      </div>
                    </button>

                    {salePayments.length > 0 && (
                      <button
                        onClick={() => setShowRecordPaymentModal(true)}
                        className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 cursor-pointer"
                      >
                        <i className="ri-money-dollar-circle-line w-4 h-4 flex items-center justify-center text-green-600"></i>
                        <div className="text-left">
                          <div className="font-medium">Record Payment</div>
                          <div className="text-xs text-gray-500">Log customer payment</div>
                        </div>
                      </button>
                    )}

                    <Link href={`/customers/${encodeURIComponent(sale.customer)}`}>
                      <button className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 cursor-pointer">
                        <i className="ri-user-line w-4 h-4 flex items-center justify-center text-purple-600"></i>
                        <div className="text-left">
                          <div className="font-medium">Customer Payments</div>
                          <div className="text-xs text-gray-500">View all customer payments</div>
                        </div>
                      </button>
                    </Link>
                  </div>
                </div>

                <button
                  onClick={() => setShowColumnModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100"
                >
                  <i className="ri-add-column-line w-4 h-4 flex items-center justify-center" />
                  <span>Add Column</span>
                </button>

                <span
                  className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border ${getStatusColor(
                    sale.status
                  )}`}
                >
                  <div
                    className={`w-2 h-2 rounded-full mr-2 ${sale.status === 'Completed'
                      ? 'bg-green-500'
                      : sale.status === 'Processing'
                      ? 'bg-blue-500'
                      : sale.status === 'Shipped'
                      ? 'bg-purple-500'
                      : sale.status === 'Delivered'
                      ? 'bg-gray-500'
                      : sale.status === 'Pending'
                      ? 'bg-yellow-500'
                      : sale.status === 'Confirmed'
                      ? 'bg-blue-500'
                      : 'bg-red-500'}`}
                  ></div>
                  {sale.status}
                </span>

                <button
                  onClick={handleEditSale}
                  className={`px-4 py-2 text-sm font-medium rounded-lg border whitespace-nowrap ${isEditing
                    ? 'bg-green-600 text-white border-green-600 hover:bg-green-700'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}`}
                >
                  {isEditing ? (
                    <>
                      <i className="ri-save-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                      Save Changes
                    </>
                  ) : (
                    <>
                      <i className="ri-edit-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                      Edit Order
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Main layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left side (order info + products) */}
            <div className="lg:col-span-2 space-y-6">
              {/* Order Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Order Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Customer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Customer
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editForm.customer}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            customer: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    ) : (
                      <div className="text-sm text-gray-900 font-medium">
                        {sale.customer}
                      </div>
                    )}
                  </div>

                  {/* Sales Rep */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sales Representative
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editForm.salesRep}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            salesRep: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    ) : (
                      <div className="text-sm text-gray-900 font-medium">
                        {sale.salesRep || sale.vendor}
                      </div>
                    )}
                  </div>

                  {/* Delivery Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Delivery Date
                    </label>
                    {isEditing ? (
                      <input
                        type="date"
                        value={editForm.deliveryDate}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            deliveryDate: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      />
                    ) : (
                      <div className="text-sm text-gray-900">
                        {sale.deliveryDate || 'Not specified'}
                      </div>
                    )}
                  </div>

                  {/* Status */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    {isEditing ? (
                      <select
                        value={editForm.status}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            status: e.target.value
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
                      >
                        <option value="Pending">Pending</option>
                        <option value="Confirmed">Confirmed</option>
                        <option value="Processing">Processing</option>
                        <option value="Shipped">Shipped</option>
                        <option value="Delivered">Delivered</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                      </select>
                    ) : (
                      <div
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                          sale.status
                        )}`}
                      >
                        <div
                          className={`w-2 h-2 rounded-full mr-2 ${sale.status === 'Completed'
                            ? 'bg-green-500'
                            : sale.status === 'Processing'
                            ? 'bg-blue-500'
                            : sale.status === 'Shipped'
                            ? 'bg-purple-500'
                            : sale.status === 'Delivered'
                            ? 'bg-gray-500'
                            : sale.status === 'Pending'
                            ? 'bg-yellow-500'
                            : sale.status === 'Confirmed'
                            ? 'bg-blue-500'
                            : 'bg-red-500'}`}
                        ></div>
                        {sale.status}
                      </div>
                    )}
                  </div>
                </div>

                {/* Order meta */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Order Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Order Date
                      </label>
                      <div className="text-sm text-gray-900">
                        {new Date(sale.salesDate).toLocaleDateString()}
                      </div>
                    </div>

                    {/* Order ID */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Order ID
                      </label>
                      <div className="text-sm text-gray-900 font-mono">
                        {sale.id}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Status Section */}
              {salePayments.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Payment Status</h3>
                    <div className="text-sm text-gray-500">
                      {paymentSummary.paymentProgress.toFixed(1)}% Paid
                    </div>
                  </div>

                  {/* Payment Progress Bar */}
                  <div className="mb-6">
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                      <span>Payment Progress</span>
                      <span>${paymentSummary.totalPaid.toLocaleString()} / ${paymentSummary.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-green-500 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(paymentSummary.paymentProgress, 100)}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Payment Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-blue-600">Total Amount</p>
                          <p className="text-xl font-bold text-blue-900">${paymentSummary.totalAmount.toLocaleString()}</p>
                        </div>
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <i className="ri-money-dollar-circle-line w-5 h-5 flex items-center justify-center text-blue-600"></i>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-green-600">Total Paid</p>
                          <p className="text-xl font-bold text-green-900">${paymentSummary.totalPaid.toLocaleString()}</p>
                        </div>
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <i className="ri-check-line w-5 h-5 flex items-center justify-center text-green-600"></i>
                        </div>
                      </div>
                    </div>

                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-red-600">Outstanding</p>
                          <p className="text-xl font-bold text-red-900">${paymentSummary.totalOutstanding.toLocaleString()}</p>
                        </div>
                        <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                          <i className="ri-alert-line w-5 h-5 flex items-center justify-center text-red-600"></i>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Records */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outstanding</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {salePayments.map((payment) => (
                          <tr key={payment.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className="text-sm font-semibold text-blue-600">{payment.invoiceNumber}</span>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm font-semibold text-gray-900">${payment.amount.toLocaleString()}</div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm font-semibold text-green-600">${payment.paidAmount.toLocaleString()}</div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className={`text-sm font-semibold ${payment.remainingAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                ${payment.remainingAmount.toLocaleString()}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="flex items-center space-x-2">
                                <i className="ri-calendar-line w-4 h-4 flex items-center justify-center text-gray-400"></i>
                                <span className={`text-sm ${payment.status === 'Overdue' ? 'text-red-600 font-medium' : 'text-gray-700'}`}>
                                  {new Date(payment.dueDate).toLocaleDateString()}
                                </span>
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPaymentStatusColor(payment.status)}`}>
                                <div className={`w-2 h-2 rounded-full mr-1 ${
                                  payment.status === 'Paid' ? 'bg-green-500' :
                                  payment.status === 'Partial' ? 'bg-yellow-500' :
                                  payment.status === 'Pending' ? 'bg-blue-500' : 'bg-red-500'
                                }`}></div>
                                {payment.status}
                              </span>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-center">
                              {payment.remainingAmount > 0 && (
                                <button
                                  onClick={() => {
                                    setRecordPaymentData(prev => ({ ...prev, paymentId: payment.id }));
                                    setShowRecordPaymentModal(true);
                                  }}
                                  className="text-green-600 hover:text-green-800 font-medium text-xs cursor-pointer whitespace-nowrap"
                                >
                                  Record Payment
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Products Table */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Products
                  </h3>
                  <button
                    onClick={() => setShowAddProductModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                  >
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Add Product</span>
                  </button>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Photo
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sale.products.map((product, idx) => (
                        <tr key={idx} className="hover:bg-gray-50">
                          {/* Photo */}
                          <td className="px-6 py-4">
                            <div className="relative group">
                              {product.photo ? (
                                <div className="relative">
                                  <img
                                    src={product.photo}
                                    alt={product.name}
                                    className="w-16 h-12 object-cover rounded-lg border border-gray-200"
                                  />
                                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-lg cursor-pointer">
                                    <label
                                      htmlFor={`photo-${idx}`}
                                      className="text-white text-xs"
                                    >
                                      <i className="ri-camera-line w-4 h-4 flex items-center justify-center" />
                                    </label>
                                  </div>
                                </div>
                              ) : (
                                <div className="w-16 h-12 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center group-hover:border-gray-400 transition-colors">
                                  <label
                                    htmlFor={`photo-${idx}`}
                                    className="cursor-pointer"
                                  >
                                    <i className="ri-image-add-line w-5 h-5 flex items-center justify-center text-gray-400 group-hover:text-gray-600" />
                                  </label>
                                </div>
                              )}
                              <input
                                id={`photo-${idx}`}
                                type="file"
                                accept="image/*"
                                onChange={e => handlePhotoUpload(idx, e)}
                                className="hidden"
                              />
                            </div>
                          </td>

                          {/* Product name */}
                          <td className="px-6 py-4">
                            {editingProduct === `product-${idx}` ? (
                              <input
                                type="text"
                                value={productEditForm.name}
                                onChange={e =>
                                  setProductEditForm(prev => ({
                                    ...prev,
                                    name: e.target.value
                                  }))
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                            ) : (
                              <div className="text-sm font-medium text-gray-900">
                                {product.name}
                              </div>
                            )}
                          </td>

                          {/* Unit Price */}
                          <td className="px-6 py-4">
                            {editingProduct === `product-${idx}` ? (
                              <input
                                type="text"
                                value={productEditForm.unitPrice}
                                onChange={e =>
                                  setProductEditForm(prev => ({
                                    ...prev,
                                    unitPrice: e.target.value
                                  }))
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                            ) : (
                              <div className="text-sm text-gray-900">
                                {product.unitPrice}
                              </div>
                            )}
                          </td>

                          {/* Quantity */}
                          <td className="px-6 py-4">
                            {editingProduct === `product-${idx}` ? (
                              <input
                                type="number"
                                min="1"
                                value={productEditForm.quantity}
                                onChange={e =>
                                  setProductEditForm(prev => ({
                                    ...prev,
                                    quantity: parseInt(e.target.value) || 0
                                  }))
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                              />
                            ) : (
                              <div className="text-sm text-gray-900">
                                {product.quantity}
                              </div>
                            )}
                          </td>

                          {/* Total */}
                          <td className="px-6 py-4">
                            <div className="text-sm font-semibold text-green-600">
                              {product.total}
                            </div>
                          </td>

                          {/* Actions */}
                          <td className="px-6 py-4 text-center">
                            <button
                              onClick={() => handleEditProduct(idx)}
                              className={`px-3 py-1.5 text-xs font-medium rounded whitespace-nowrap ${editingProduct === `product-${idx}`
                                ? 'bg-green-600 text-white hover:bg-green-700'
                                : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                            >
                              {editingProduct === `product-${idx}` ? (
                                <>
                                  <i className="ri-save-line w-3 h-3 flex items-center justify-center mr-1 inline" />
                                  Save
                                </>
                              ) : (
                                <>
                                  <i className="ri-edit-line w-3 h-3 flex items-center justify-center mr-1 inline" />
                                  Edit
                                </>
                              )}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Right side (summary + actions) */}
            <div className="space-y-6">
              {/* Financial Summary */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Financial Summary
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm text-gray-600">Total Amount</span>
                    <span className="text-lg font-bold text-gray-900">{sale.total}</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-sm text-gray-600">Net Amount</span>
                    <span className="text-lg font-bold text-green-600">{sale.total}</span>
                  </div>
                  {salePayments.length > 0 && (
                    <>
                      <div className="flex justify-between items-center py-2 border-t border-gray-100">
                        <span className="text-sm text-gray-600">Amount Paid</span>
                        <span className="text-lg font-bold text-green-600">${paymentSummary.totalPaid.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-sm text-gray-600">Outstanding</span>
                        <span className="text-lg font-bold text-red-600">${paymentSummary.totalOutstanding.toLocaleString()}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <button className="w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                    <i className="ri-printer-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Print Invoice
                  </button>
                  <button className="w-full px-4 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100">
                    <i className="ri-download-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Download PDF
                  </button>
                  <button className="w-full px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100">
                    <i className="ri-mail-line w-4 h-4 flex items-center justify-center mr-2 inline" />
                    Email Customer
                  </button>
                </div>
              </div>

              {/* Order Timeline */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Order Timeline
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        Order Created
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(sale.salesDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  {sale.status === 'Confirmed' && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Order Confirmed
                        </div>
                        <div className="text-xs text-gray-500">Today</div>
                      </div>
                    </div>
                  )}
                  {(sale.status === 'Processing' ||
                    sale.status === 'Shipped' ||
                    sale.status === 'Delivered' ||
                    sale.status === 'Completed') && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Processing Started
                        </div>
                        <div className="text-xs text-gray-500">Today</div>
                      </div>
                    </div>
                  )}
                  {(sale.status === 'Shipped' ||
                    sale.status === 'Delivered' ||
                    sale.status === 'Completed') && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Order Shipped
                        </div>
                        <div className="text-xs text-gray-500">Today</div>
                      </div>
                    </div>
                  )}
                  {(sale.status === 'Delivered' ||
                    sale.status === 'Completed') && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-gray-500 rounded-full mt-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Order Delivered
                        </div>
                        <div className="text-xs text-gray-500">
                          {sale.deliveryDate || 'Today'}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Create Invoice Modal */}
      {showCreateInvoiceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Create Invoice</h3>
              <button
                onClick={() => setShowCreateInvoiceModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <i className="ri-information-line w-5 h-5 flex items-center justify-center text-blue-600"></i>
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">Invoice Details</p>
                    <p>Order: {sale?.id}</p>
                    <p>Customer: {sale?.customer}</p>
                    <p>Amount: {sale?.total}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Due Date *</label>
                <input
                  type="date"
                  required
                  value={paymentData.dueDate}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, dueDate: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <select
                  value={paymentData.method}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, method: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm pr-8"
                >
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Credit Card">Credit Card</option>
                  <option value="Cash">Cash</option>
                  <option value="Check">Check</option>
                  <option value="PayPal">PayPal</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                <textarea
                  rows={3}
                  value={paymentData.notes}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add any notes about this invoice..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
                />
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowCreateInvoiceModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleCreateInvoice}
                  disabled={isProcessingPayment || !paymentData.dueDate}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
                >
                  {isProcessingPayment ? (
                    <>
                      <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                      <span>Creating...</span>
                    </>
                  ) : (
                    <>
                      <i className="ri-file-add-line w-4 h-4 flex items-center justify-center"></i>
                      <span>Create Invoice</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Record Payment Modal */}
      {showRecordPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Record Payment</h3>
              <button
                onClick={() => setShowRecordPaymentModal(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
              </button>
            </div>

            <div className="space-y-4">
              {/* Payment Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Invoice *</label>
                <select
                  required
                  value={recordPaymentData.paymentId}
                  onChange={(e) => setRecordPaymentData(prev => ({ ...prev, paymentId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm pr-8"
                >
                  <option value="">Select an invoice</option>
                  {salePayments.filter(p => p.remainingAmount > 0).map(payment => (
                    <option key={payment.id} value={payment.id}>
                      {payment.invoiceNumber} - Outstanding: ${payment.remainingAmount.toLocaleString()}
                    </option>
                  ))}
                </select>
              </div>

              {/* Payment Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Amount *</label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                  <input
                    type="number"
                    required
                    min="0.01"
                    step="0.01"
                    value={recordPaymentData.amount}
                    onChange={(e) => setRecordPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder="0.00"
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                  />
                </div>
                {recordPaymentData.paymentId && (
                  <p className="mt-1 text-xs text-gray-500">
                    Maximum: ${salePayments.find(p => p.id === recordPaymentData.paymentId)?.remainingAmount.toLocaleString() || '0'}
                  </p>
                )}
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method *</label>
                <select
                  required
                  value={recordPaymentData.method}
                  onChange={(e) => setRecordPaymentData(prev => ({ ...prev, method: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm pr-8"
                >
                  <option value="Cash">Cash</option>
                  <option value="Credit Card">Credit Card</option>
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Check">Check</option>
                  <option value="PayPal">PayPal</option>
                </select>
              </div>

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                <textarea
                  rows={3}
                  value={recordPaymentData.notes}
                  onChange={(e) => setRecordPaymentData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add any notes about this payment..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm resize-none"
                />
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowRecordPaymentModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleRecordPayment}
                  disabled={isProcessingPayment || !recordPaymentData.paymentId || !recordPaymentData.amount}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
                >
                  {isProcessingPayment ? (
                    <>
                      <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                      <span>Recording...</span>
                    </>
                  ) : (
                    <>
                      <i className="ri-money-dollar-circle-line w-4 h-4 flex items-center justify-center"></i>
                      <span>Record Payment</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}