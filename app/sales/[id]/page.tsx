
import SalesDetailClient from './SalesDetailClient';

export async function generateStaticParams() {
  // Generate a comprehensive range of sales order IDs to prevent 404 errors
  // This covers both existing and future sales orders
  const params = [];
  
  // 2024 sales orders (SO-2024-001 to SO-2024-050)
  for (let i = 1; i <= 50; i++) {
    params.push({ id: `SO-2024-${String(i).padStart(3, '0')}` });
  }
  
  // 2025 sales orders (SO-2025-001 to SO-2025-100)
  for (let i = 1; i <= 100; i++) {
    params.push({ id: `SO-2025-${String(i).padStart(3, '0')}` });
  }
  
  // 2026 sales orders (SO-2026-001 to SO-2026-050) - for future testing
  for (let i = 1; i <= 50; i++) {
    params.push({ id: `SO-2026-${String(i).padStart(3, '0')}` });
  }
  
  return params;
}

export default function SalesDetailPage({ params }: { params: { id: string } }) {
  return <SalesDetailClient saleId={params.id} />;
}
