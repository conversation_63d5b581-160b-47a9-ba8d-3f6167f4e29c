
'use client';

import { useState, useRef } from 'react';
import * as XLSX from 'xlsx';

interface ExcelImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (products: any[]) => void;
  customColumns: Array<{ id: string; name: string; type: string }>;
}

interface ColumnMapping {
  [key: number]: string;
}

export default function ExcelImportModal({ 
  isOpen, 
  onClose, 
  onImport, 
  customColumns 
}: ExcelImportModalProps) {
  const [excelData, setExcelData] = useState<any[][]>([]);
  const [selectedTableRange, setSelectedTableRange] = useState<{startRow: number, endRow: number} | null>(null);
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({});
  const [importWithPhotos, setImportWithPhotos] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [fileName, setFileName] = useState('');
  const [currentStep, setCurrentStep] = useState<'upload' | 'view' | 'select' | 'map'>('upload');
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);

  const predefinedFields = [
    { value: 'name', label: 'Product Name' },
    { value: 'unitPrice', label: 'Unit Price' },
    { value: 'quantity', label: 'Quantity' },
    { value: 'photo', label: 'Photo URL/Description' },
    { value: 'sku', label: 'SKU/Code' },
    { value: 'description', label: 'Description' },
    { value: 'category', label: 'Category' },
    { value: 'brand', label: 'Brand' },
    { value: 'supplier', label: 'Supplier' },
    { value: 'weight', label: 'Weight' },
    { value: 'dimensions', label: 'Dimensions' },
    { value: 'ctns', label: 'CTNS/Cartons' },
    { value: 'roll', label: 'Roll/Units' },
    { value: 'shippingType', label: 'Shipping Type' },
    { value: 'total', label: 'Total Amount' },
    { value: 'customerInfo', label: 'Customer Info' },
    { value: 'salesRep', label: 'Sales Representative' },
    { value: 'status', label: 'Status' }
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setFileName(file.name);
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get first worksheet
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(firstSheet, { 
          header: 1,
          defval: '',
          raw: false,
          range: undefined // Import all data including merged cells
        }) as any[][];

        // Keep all data without processing
        const allData = jsonData.map(row => 
          row.map(cell => cell === null || cell === undefined ? '' : cell.toString())
        );
        
        setExcelData(allData);
        setCurrentStep('view');
        setSelectedRows(new Set());
        setSelectedTableRange(null);
        setColumnMapping({});
      } catch (error) {
        console.error('Error reading Excel file:', error);
        alert('Error reading Excel file. Please make sure it\'s a valid Excel file.');
      }
    };

    reader.readAsArrayBuffer(file);
  };

  const toggleRowSelection = (rowIndex: number) => {
    const newSelectedRows = new Set(selectedRows);
    if (newSelectedRows.has(rowIndex)) {
      newSelectedRows.delete(rowIndex);
    } else {
      newSelectedRows.add(rowIndex);
    }
    setSelectedRows(newSelectedRows);
  };

  const selectRowRange = (startRow: number, endRow: number) => {
    const newSelectedRows = new Set<number>();
    for (let i = startRow; i <= endRow; i++) {
      newSelectedRows.add(i);
    }
    setSelectedRows(newSelectedRows);
  };

  const confirmTableSelection = () => {
    if (selectedRows.size === 0) {
      alert('Please select the rows that contain your product table.');
      return;
    }

    const sortedRows = Array.from(selectedRows).sort((a, b) => a - b);
    const startRow = sortedRows[0];
    const endRow = sortedRows[sortedRows.length - 1];
    
    setSelectedTableRange({ startRow, endRow });
    
    // Auto-detect column mapping from the first selected row
    const headerRow = excelData[startRow] || [];
    autoDetectColumns(headerRow);
    
    setCurrentStep('map');
  };

  const autoDetectColumns = (headerRow: any[]) => {
    const autoMapping: ColumnMapping = {};

    headerRow.forEach((header, index) => {
      if (!header) return;
      
      const lowerHeader = header.toString().toLowerCase().trim();

      if (lowerHeader.includes('product') || lowerHeader.includes('name') || lowerHeader.includes('item') || lowerHeader.includes('title')) {
        autoMapping[index] = 'name';
      } else if (lowerHeader.includes('unit price') || lowerHeader.includes('price') || lowerHeader.includes('cost') || lowerHeader.includes('amount')) {
        autoMapping[index] = 'unitPrice';  
      } else if (lowerHeader.includes('quantity') || lowerHeader.includes('qty') || lowerHeader.includes('stock')) {
        autoMapping[index] = 'quantity';
      } else if (lowerHeader.includes('photo') || lowerHeader.includes('image') || lowerHeader.includes('url') || lowerHeader.includes('pic')) {
        autoMapping[index] = 'photo';
      } else if (lowerHeader.includes('sku') || lowerHeader.includes('code') || lowerHeader.includes('id')) {
        autoMapping[index] = 'sku';
      } else if (lowerHeader.includes('description') || lowerHeader.includes('desc') || lowerHeader.includes('detail')) {
        autoMapping[index] = 'description';
      } else if (lowerHeader.includes('category') || lowerHeader.includes('type') || lowerHeader.includes('group')) {
        autoMapping[index] = 'category';
      } else if (lowerHeader.includes('brand') || lowerHeader.includes('manufacturer') || lowerHeader.includes('make')) {
        autoMapping[index] = 'brand';
      } else if (lowerHeader.includes('supplier') || lowerHeader.includes('vendor')) {
        autoMapping[index] = 'supplier';
      } else if (lowerHeader.includes('weight')) {
        autoMapping[index] = 'weight';
      } else if (lowerHeader.includes('dimension') || lowerHeader.includes('size')) {
        autoMapping[index] = 'dimensions';
      } else if (lowerHeader.includes('ctns') || lowerHeader.includes('carton')) {
        autoMapping[index] = 'ctns';
      } else if (lowerHeader.includes('roll') || lowerHeader.includes('unit')) {
        autoMapping[index] = 'roll';
      } else if (lowerHeader.includes('shipping') || lowerHeader.includes('delivery')) {
        autoMapping[index] = 'shippingType';
      } else if (lowerHeader.includes('total')) {
        autoMapping[index] = 'total';
      } else if (lowerHeader.includes('customer')) {
        autoMapping[index] = 'customerInfo';
      } else if (lowerHeader.includes('sales') || lowerHeader.includes('rep')) {
        autoMapping[index] = 'salesRep';
      } else if (lowerHeader.includes('status')) {
        autoMapping[index] = 'status';
      }
    });

    setColumnMapping(autoMapping);
  };

  const generateImageFromDescription = async (description: string): Promise<string> => {
    try {
      const cleanDesc = description.replace(/[^\w\s]/g, '').trim();
      return `https://readdy.ai/api/search-image?query=$%7BencodeURIComponent%28%60$%7BcleanDesc%7D%20product%20professional%20white%20background%20commercial%20photography%60%29%7D&width=400&height=300&seq=${Math.random().toString(36).substr(2, 9)}&orientation=landscape`;
    } catch (error) {
      console.error('Error generating image:', error);
      return '';
    }
  };

  const processPhotoField = async (value: string): Promise<string> => {
    if (!value || !value.trim()) return '';

    const trimmedValue = value.trim();
    
    if (trimmedValue.startsWith('http://') || trimmedValue.startsWith('https://')) {
      return trimmedValue;
    }

    return await generateImageFromDescription(trimmedValue);
  };

  const cleanPriceValue = (value: string): string => {
    if (!value) return '';
    return value.toString().replace(/[$,£€¥₹]/g, '').replace(/[^\d.]/g, '');
  };

  const cleanQuantityValue = (value: string): string => {
    if (!value) return '1';
    const cleaned = value.toString().replace(/[^\d]/g, '');
    return cleaned || '1';
  };

  const handleImport = async () => {
    if (!selectedTableRange || excelData.length === 0) return;

    setIsProcessing(true);

    try {
      const { startRow, endRow } = selectedTableRange;
      const dataRows = excelData.slice(startRow + 1, endRow + 1); // Skip header row
      
      if (dataRows.length === 0) {
        alert('No data rows found to import.');
        setIsProcessing(false);
        return;
      }

      const products = await Promise.all(
        dataRows.map(async (row, index) => {
          const hasContent = row.some(cell => cell && cell.toString().trim() !== '');
          if (!hasContent) return null;

          const product: any = {
            id: `imported-${Date.now()}-${index}`,
            name: '',
            unitPrice: '',
            quantity: '1',
            quantityUnit: 'pcs',
            photo: null,
            photoPreview: '',
            customData: {}
          };

          customColumns.forEach(col => {
            product.customData[col.id] = '';
          });

          for (const [colIndex, fieldName] of Object.entries(columnMapping)) {
            const cellValue = row[parseInt(colIndex)]?.toString().trim() || '';
            
            if (!cellValue) continue;

            switch (fieldName) {
              case 'name':
                product.name = cellValue;
                break;
              case 'unitPrice':
                product.unitPrice = cleanPriceValue(cellValue);
                break;
              case 'quantity':
                product.quantity = cleanQuantityValue(cellValue);
                break;
              case 'photo':
                if (importWithPhotos) {
                  try {
                    product.photoPreview = await processPhotoField(cellValue);
                  } catch (error) {
                    console.error('Error processing photo:', error);
                  }
                }
                break;
              default:
                if (fieldName.startsWith('custom-') || customColumns.some(col => col.id === fieldName)) {
                  product.customData[fieldName] = cellValue;
                } else {
                  product.customData[fieldName] = cellValue;
                }
                break;
            }
          }

          if (!product.name && row.length > 0) {
            for (let i = 0; i < Math.min(row.length, 3); i++) {
              const cell = row[i]?.toString().trim();
              if (cell && cell.length > 2 && !cell.match(/^\d+\.?\d*$/)) {
                product.name = cell;
                break;
              }
            }
          }

          return product;
        })
      );

      const validProducts = products.filter(product => 
        product && product.name && product.name.trim() !== ''
      );

      if (validProducts.length === 0) {
        alert('No valid products found. Please make sure at least one column is mapped to Product Name.');
        setIsProcessing(false);
        return;
      }

      onImport(validProducts);
      handleClose();
    } catch (error) {
      console.error('Error processing import:', error);
      alert('Error processing import. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    setExcelData([]);
    setSelectedTableRange(null);
    setColumnMapping({});
    setImportWithPhotos(false);
    setFileName('');
    setCurrentStep('upload');
    setSelectedRows(new Set());
    setIsProcessing(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  const getSelectedTableData = () => {
    if (!selectedTableRange) return [];
    const { startRow, endRow } = selectedTableRange;
    return excelData.slice(startRow, endRow + 1);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-7xl max-h-[95vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Excel Import - {currentStep === 'upload' ? 'Upload File' : 
                           currentStep === 'view' ? 'View Full Excel' : 
                           currentStep === 'select' ? 'Select Product Table' : 'Map Columns'}
            </h3>
            <p className="text-sm text-gray-600">
              {currentStep === 'upload' ? 'Upload your Excel file to get started' :
               currentStep === 'view' ? 'Review your complete Excel file and select the product table area' :
               currentStep === 'select' ? 'Select the rows containing your products' : 
               'Map Excel columns to product fields'}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isProcessing}
          >
            <i className="ri-close-line w-6 h-6 flex items-center justify-center"></i>
          </button>
        </div>

        {/* Step 1: Upload File */}
        {currentStep === 'upload' && (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileUpload}
              className="hidden"
              id="excel-file-input"
            />
            <label
              htmlFor="excel-file-input"
              className="cursor-pointer flex flex-col items-center space-y-3"
            >
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <i className="ri-file-excel-2-line w-8 h-8 flex items-center justify-center text-blue-600"></i>
              </div>
              <div>
                <p className="text-lg font-medium text-gray-900">Upload Your Excel File</p>
                <p className="text-sm text-gray-500">
                  We'll show you the complete Excel content first
                </p>
              </div>
              <button
                type="button"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
              >
                Select Excel File
              </button>
            </label>
          </div>
        )}

        {/* Step 2: View Complete Excel */}
        {currentStep === 'view' && excelData.length > 0 && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-medium text-blue-900">Complete Excel Content</h4>
                  <p className="text-sm text-blue-700">
                    File: {fileName} • {excelData.length} total rows
                  </p>
                </div>
                <button
                  onClick={() => setCurrentStep('select')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer whitespace-nowrap"
                >
                  Select Product Table
                </button>
              </div>
            </div>

            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <div className="overflow-x-auto max-h-96">
                <table className="min-w-full divide-y divide-gray-200">
                  <tbody className="bg-white divide-y divide-gray-200">
                    {excelData.map((row, rowIndex) => (
                      <tr 
                        key={rowIndex} 
                        className={`${rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-yellow-50 cursor-pointer`}
                        onClick={() => toggleRowSelection(rowIndex)}
                      >
                        <td className="px-3 py-2 text-xs font-medium text-gray-500 w-16">
                          <div className="flex items-center space-x-2">
                            <span>Row {rowIndex + 1}</span>
                            {selectedRows.has(rowIndex) && (
                              <i className="ri-check-line w-3 h-3 flex items-center justify-center text-blue-600"></i>
                            )}
                          </div>
                        </td>
                        {row.map((cell, cellIndex) => (
                          <td key={cellIndex} className="px-3 py-2 text-xs text-gray-900 max-w-32 border-r border-gray-100">
                            <div className="truncate" title={cell?.toString() || ''}>
                              {cell?.toString() || ''}
                            </div>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h5 className="text-sm font-medium text-gray-900 mb-2">Quick Selection Tools:</h5>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => selectRowRange(0, 4)}
                  className="px-3 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Select Rows 1-5
                </button>
                <button
                  onClick={() => selectRowRange(5, 10)}
                  className="px-3 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Select Rows 6-11
                </button>
                <button
                  onClick={() => setSelectedRows(new Set())}
                  className="px-3 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50 cursor-pointer whitespace-nowrap"
                >
                  Clear Selection
                </button>
              </div>
              {selectedRows.size > 0 && (
                <div className="mt-2 flex items-center justify-between">
                  <p className="text-xs text-gray-600">
                    {selectedRows.size} rows selected
                  </p>
                  <button
                    onClick={confirmTableSelection}
                    className="px-4 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 cursor-pointer whitespace-nowrap"
                  >
                    Confirm Selection
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Step 3: Column Mapping */}
        {currentStep === 'map' && selectedTableRange && (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-lg font-medium text-green-900">Selected Product Table</h4>
              <p className="text-sm text-green-700">
                Rows {selectedTableRange.startRow + 1} to {selectedTableRange.endRow + 1} • {selectedTableRange.endRow - selectedTableRange.startRow + 1} rows selected
              </p>
            </div>

            {/* Column Mapping */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-medium text-gray-900">Map Columns to Product Fields</h4>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={importWithPhotos}
                    onChange={(e) => setImportWithPhotos(e.target.checked)}
                    className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                  />
                  <span className="text-sm font-medium text-gray-900">Process Photos</span>
                </label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getSelectedTableData()[0]?.map((header, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="mb-3">
                      <label className="text-sm font-medium text-gray-600">
                        Column {index + 1}
                      </label>
                      <div className="text-sm text-gray-900 font-medium truncate" title={header?.toString() || `Column ${index + 1}`}>
                        {header?.toString() || `Column ${index + 1}`}
                      </div>
                      <div className="text-xs text-gray-500 mt-1 bg-gray-50 rounded px-2 py-1">
                        Sample: {getSelectedTableData()[1]?.[index]?.toString().substring(0, 25) || 'No data'}
                      </div>
                    </div>
                    
                    <select
                      value={columnMapping[index] || ''}
                      onChange={(e) => setColumnMapping(prev => ({
                        ...prev,
                        [index]: e.target.value
                      }))}
                      className="w-full text-xs border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500 pr-6"
                    >
                      <option value="">Skip Column</option>
                      <optgroup label="🎯 Core Product Fields">
                        {predefinedFields.slice(0, 6).map(field => (
                          <option key={field.value} value={field.value}>
                            {field.label}
                          </option>
                        ))}
                      </optgroup>
                      <optgroup label="📋 Additional Fields">
                        {predefinedFields.slice(6).map(field => (
                          <option key={field.value} value={field.value}>
                            {field.label}
                          </option>
                        ))}
                      </optgroup>
                      {customColumns.length > 0 && (
                        <optgroup label="⚙️ Custom Columns">
                          {customColumns.map(col => (
                            <option key={col.id} value={col.id}>
                              {col.name} (Custom)
                            </option>
                          ))}
                        </optgroup>
                      )}
                    </select>
                    
                    {columnMapping[index] && (
                      <div className="mt-2 text-xs text-green-600 flex items-center space-x-1">
                        <i className="ri-check-line w-3 h-3 flex items-center justify-center"></i>
                        <span>Mapped</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Preview Selected Data */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Preview Selected Data</h4>
              <div className="border border-gray-300 rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
                      <tr>
                        {getSelectedTableData()[0]?.map((header, index) => (
                          <th key={index} className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <div className="flex flex-col space-y-1">
                              <span>Col {index + 1}</span>
                              <div className={`px-2 py-1 rounded text-xs font-semibold ${
                                columnMapping[index] 
                                  ? 'bg-blue-100 text-blue-800' 
                                  : 'bg-gray-100 text-gray-500'
                              }`}>
                                {columnMapping[index] ? (
                                  predefinedFields.find(f => f.value === columnMapping[index])?.label ||
                                  customColumns.find(c => c.id === columnMapping[index])?.name ||
                                  columnMapping[index]
                                ) : 'Skip'}
                              </div>
                            </div>
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSelectedTableData().slice(1, 6).map((row, rowIndex) => (
                        <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          {row.map((cell, cellIndex) => (
                            <td key={cellIndex} className="px-3 py-2 text-xs text-gray-900 max-w-32">
                              <div className="truncate" title={cell?.toString() || ''}>
                                {cell?.toString() || ''}
                              </div>
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            {currentStep === 'view' && `${excelData.length} total rows • ${selectedRows.size} selected`}
            {currentStep === 'map' && selectedTableRange && `${Object.keys(columnMapping).length} columns mapped • ${selectedTableRange.endRow - selectedTableRange.startRow} products`}
          </div>
          
          <div className="flex items-center space-x-3">
            {currentStep !== 'upload' && (
              <button
                onClick={() => {
                  if (currentStep === 'view') setCurrentStep('upload');
                  if (currentStep === 'select') setCurrentStep('view');
                  if (currentStep === 'map') setCurrentStep('view');
                }}
                disabled={isProcessing}
                className="px-4 py-2 text-gray-700 hover:text-gray-900 disabled:opacity-50 cursor-pointer whitespace-nowrap"
              >
                Back
              </button>
            )}

            <button
              onClick={handleClose}
              disabled={isProcessing}
              className="px-4 py-2 text-gray-700 hover:text-gray-900 disabled:opacity-50 cursor-pointer whitespace-nowrap"
            >
              Cancel
            </button>
            
            {currentStep === 'map' && (
              <button
                onClick={handleImport}
                disabled={isProcessing || Object.keys(columnMapping).length === 0}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed cursor-pointer whitespace-nowrap flex items-center space-x-2"
              >
                {isProcessing ? (
                  <>
                    <i className="ri-loader-4-line w-4 h-4 flex items-center justify-center animate-spin"></i>
                    <span>Importing Products...</span>
                  </>
                ) : (
                  <>
                    <i className="ri-upload-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Import {selectedTableRange ? selectedTableRange.endRow - selectedTableRange.startRow : 0} Products</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
