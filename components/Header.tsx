
'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import LanguageSwitcher from './LanguageSwitcher';
import { useTranslation } from '@/hooks/useTranslation';

interface HeaderProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export default function Header({ sidebarOpen, setSidebarOpen }: HeaderProps) {
  const { t } = useTranslation();
  const pathname = usePathname();
  const router = useRouter();
  const [navMode, setNavMode] = useState<'sidebar' | 'topnav'>('sidebar');
  
  // Load saved navigation mode from localStorage on component mount
  useEffect(() => {
    const savedNavMode = localStorage.getItem('navMode') as 'sidebar' | 'topnav' | null;
    if (savedNavMode) {
      setNavMode(savedNavMode);
    }
  }, []);

  // Save navigation mode to localStorage whenever it changes
  const handleNavModeChange = (newMode: 'sidebar' | 'topnav') => {
    setNavMode(newMode);
    localStorage.setItem('navMode', newMode);
  };
  
  const getPageTitle = () => {
    switch (pathname) {
      case '/':
        return 'Dashboard';
      case '/orders':
        return 'Master order';
      case '/sales':
        return 'Sales';
      case '/products':
        return 'Products';
      case '/customers':
        return 'Customers';
      case '/crm':
        return 'CRM';
      case '/reports':
        return 'Reports';
      case '/settings':
        return 'Settings';
      case '/new':
        return 'Create New Order';
      case '/journal':
        return 'Journal Entries';
      default:
        if (pathname.startsWith('/customers/')) {
          return 'Customer Details';
        }
        if (pathname.startsWith('/journal/')) {
          return 'Journal Entry';
        }
        return 'Dashboard';
    }
  };

  const navigationItems = [
    { href: '/', icon: 'ri-dashboard-line', label: 'Dashboard' },
    { href: '/orders', icon: 'ri-file-list-3-line', label: 'Master order' },
    { href: '/sales', icon: 'ri-line-chart-line', label: 'Sales' },
    { href: '/products', icon: 'ri-shopping-bag-line', label: 'Products' },
    { href: '/customers', icon: 'ri-user-line', label: 'Customers' },
    { href: '/reports', icon: 'ri-bar-chart-line', label: 'Reports' },
    { href: '/settings', icon: 'ri-settings-line', label: 'Settings' }
  ];

  const navItems = [
    { name: 'Dashboard', path: '/', icon: 'ri-dashboard-line' },
    { name: 'Sales', path: '/sales', icon: 'ri-line-chart-line' },
    { name: 'Products', path: '/products', icon: 'ri-box-3-line' },
    { name: 'Customers', path: '/customers', icon: 'ri-user-line' },
    { name: 'Payments', path: '/payments', icon: 'ri-money-dollar-circle-line' },
    { name: 'Orders', path: '/orders', icon: 'ri-shopping-cart-line' },
  ];

  const isActiveLink = (href: string) => {
    return pathname === href;
  };

  if (navMode === 'topnav') {
    return (
      <>
        {/* Top Navigation Layout */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          {/* Main Navigation Bar */}
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Logo and Brand */}
              <div className="flex items-center space-x-8">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl font-bold text-gray-900" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }}>
                    vera
                  </div>
                </div>

                {/* Navigation Links */}
                <nav className="hidden lg:flex items-center space-x-1">
                  <Link 
                    href="/" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-dashboard-line"></i>
                    Dashboard
                  </Link>
                  <Link 
                    href="/sales" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/sales' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-bar-chart-line"></i>
                    Sales
                  </Link>
                  <Link 
                    href="/purchase" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/purchase' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-shopping-bag-line"></i>
                    Purchase
                  </Link>
                  <Link 
                    href="/journal" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/journal' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-book-line"></i>
                    Journal
                  </Link>
                  <Link 
                    href="/orders" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/orders' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-shopping-cart-line"></i>
                    Orders
                  </Link>
                  <Link 
                    href="/crm" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/crm' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-customer-service-line"></i>
                    CRM
                  </Link>
                  <Link 
                    href="/customers" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/customers' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-user-line"></i>
                    Customers
                  </Link>
                  <Link 
                    href="/products" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/products' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-box-3-line"></i>
                    Products
                  </Link>
                  <Link 
                    href="/payments" 
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap ${
                      pathname === '/payments' ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className="ri-money-dollar-circle-line"></i>
                    Payments
                  </Link>
                </nav>
              </div>

              {/* Right Side Actions */}
              <div className="flex items-center space-x-4">
                <LanguageSwitcher />
                
                <div className="relative">
                  <div className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer">
                    <i className="ri-notification-3-line w-5 h-5 flex items-center justify-center"></i>
                    <span className="hidden md:block">Notifications</span>
                    <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                  </div>
                </div>

                <div className="relative">
                  <div className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer">
                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-sm font-semibold text-blue-600">JS</span>
                    </div>
                    <span className="hidden md:block">John Smith</span>
                    <i className="ri-arrow-down-s-line w-4 h-4 flex items-center justify-center"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Page Title Bar */}
          <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <h1 className="text-lg font-semibold text-gray-900">{getPageTitle()}</h1>
          </div>
        </div>

        {/* Mobile Navigation Sidebar (when topnav is active) */}
        {sidebarOpen && (
          <>
            <div 
              className="fixed inset-0 bg-black bg-opacity-25 z-40 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
            <div className="fixed left-0 top-0 h-full bg-white border-r border-gray-200 w-64 z-50 lg:hidden">
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="text-3xl font-bold text-gray-900" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }}>
                    vera
                  </div>
                  <button
                    onClick={() => setSidebarOpen(false)}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors"
                  >
                    <i className="ri-close-line w-5 h-5 flex items-center justify-center"></i>
                  </button>
                </div>
              </div>

              <nav className="mt-8">
                <div className="space-y-1 px-4">
                  <Link href="/" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname === '/' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-dashboard-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>Dashboard</span>
                  </Link>

                  <Link 
                    href="/sales" 
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      pathname === '/sales' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <i className="ri-bar-chart-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    Sales
                  </Link>

                  <Link 
                    href="/purchase" 
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      pathname === '/purchase' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <i className="ri-shopping-bag-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    Purchase
                  </Link>

                  <Link 
                    href="/journal" 
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      pathname === '/journal' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <i className="ri-book-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    Journal
                  </Link>

                  <Link href="/orders" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname === '/orders' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-shopping-cart-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>Orders</span>
                  </Link>

                  <Link href="/crm" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname === '/crm' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-customer-service-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>CRM</span>
                  </Link>

                  <Link href="/customers" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname?.startsWith('/customers') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-user-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>Customers</span>
                  </Link>

                  <Link href="/products" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname === '/products' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-box-3-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>Products</span>
                  </Link>

                  <Link href="/receive-money" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname === '/receive-money' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-money-dollar-circle-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>Receive Money</span>
                  </Link>

                  <Link href="/payments" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    pathname === '/payments' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}>
                    <i className="ri-bill-line w-5 h-5 flex items-center justify-center mr-3"></i>
                    <span>Payments</span>
                  </Link>
                </div>
              </nav>
            </div>
          </>
        )}
      </>
    );
  }

  // Sidebar Navigation Layout (existing code)
  return (
    <>
      {/* Left Sidebar */}
      <div className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 transition-all duration-300 z-50 ${
        sidebarOpen ? 'w-64' : 'w-0 overflow-hidden'
      }`}>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-3xl font-bold text-gray-900" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }}>
                vera
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors"
            >
              <i className="ri-menu-fold-line w-5 h-5 flex items-center justify-center"></i>
            </button>
          </div>
        </div>

        <nav className="mt-8">
          <div className="space-y-1 px-4">
            <Link href="/" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname === '/' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-dashboard-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Dashboard</span>
            </Link>

            <Link 
              href="/sales" 
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                pathname === '/sales' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <i className="ri-bar-chart-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Sales</span>
            </Link>

            <Link 
              href="/purchase" 
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                pathname === '/purchase' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <i className="ri-shopping-bag-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Purchase</span>
            </Link>

            <Link 
              href="/journal" 
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                pathname === '/journal' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <i className="ri-book-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Journal</span>
            </Link>

            <Link href="/orders" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname === '/orders' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-shopping-cart-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Orders</span>
            </Link>

            {/* Inserted CRM link before Customers */}
            <Link href="/crm" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname === '/crm' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-customer-service-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>CRM</span>
            </Link>

            {/* Updated Customers link */}
            <Link href="/customers" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname?.startsWith('/customers') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-user-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>{t('nav.customers')}</span>
            </Link>

            <Link href="/products" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname === '/products' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-box-3-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Products</span>
            </Link>

            <Link href="/receive-money" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname === '/receive-money' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-money-dollar-circle-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Receive Money</span>
            </Link>

            <Link href="/payments" className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              pathname === '/payments' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            }`}>
              <i className="ri-bill-line w-5 h-5 flex items-center justify-center mr-3"></i>
              <span className={sidebarOpen ? '' : 'hidden'}>Payments</span>
            </Link>
          </div>
        </nav>

        <div className="absolute bottom-6 left-4 right-4">
          <div className="flex items-center px-4 py-3 bg-gray-50 rounded-lg space-x-3">
            <div className="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
              <span className="text-teal-600 text-sm font-semibold">U</span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">User</div>
              <div className="text-xs text-gray-500">Administrator</div>
            </div>
          </div>
        </div>

        {/* Navigation Mode Toggle in Sidebar */}
        <div className="absolute top-20 right-4">
          <div className="relative group">
            <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg cursor-pointer">
              <i className="ri-layout-line w-4 h-4 flex items-center justify-center"></i>
            </button>
            
            <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 min-w-[160px] py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                Layout Mode
              </div>
              
              <button
                onClick={() => handleNavModeChange('sidebar')}
                className={`w-full flex items-center space-x-3 px-4 py-3 text-sm cursor-pointer transition-colors ${
                  navMode === 'sidebar' 
                    ? 'text-teal-600 bg-teal-50' 
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <i className="ri-sidebar-unfold-line w-4 h-4 flex items-center justify-center"></i>
                <span>Sidebar</span>
              </button>
              
              <button
                onClick={() => handleNavModeChange('topnav')}
                className={`w-full flex items-center space-x-3 px-4 py-3 text-sm cursor-pointer transition-colors ${
                  navMode === 'topnav' 
                    ? 'text-teal-600 bg-teal-50' 
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <i className="ri-navigation-line w-4 h-4 flex items-center justify-center"></i>
                <span>Top Nav</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Menu Button */}
      {!sidebarOpen && (
        <button
          onClick={() => setSidebarOpen(true)}
          className="fixed left-4 top-4 z-50 w-12 h-12 bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center cursor-pointer hover:bg-gray-50"
        >
          <i className="ri-menu-line w-5 h-5 flex items-center justify-center text-gray-700"></i>
        </button>
      )}

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-25 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Top Bar */}
      <div className={`bg-white border-b border-gray-200 transition-all duration-300 ${
        sidebarOpen ? 'pl-64' : 'pl-0'
      }`}>
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {!sidebarOpen && <div className="w-16"></div>}
              <h1 className="text-lg font-semibold text-gray-900">{getPageTitle()}</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              
              <div className="relative">
                <div className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer">
                  <i className="ri-notification-3-line w-5 h-5 flex items-center justify-center"></i>
                  <span className="hidden md:block">Notifications</span>
                  <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                </div>
              </div>

              <div className="relative">
                <div className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-sm font-semibold text-blue-600">JS</span>
                  </div>
                  <span className="hidden md:block">John Smith</span>
                  <i className="ri-arrow-down-s-line w-4 h-4 flex items-center justify-center"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
