'use client';

import { useState, useRef, useEffect } from 'react';

interface SupplierDropdownProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
}

const defaultSuppliers = [
  'TechSource Ltd',
  'Global Supplies Inc',
  'Premium Materials Co',
  'Industrial Solutions',
  'Quality Parts Ltd',
  'Reliable Vendors Inc',
  'Best Price Supplies',
  'Swift Delivery Co',
  'Professional Equipment',
  'Modern Components Ltd'
];

export default function SupplierDropdown({ value, onChange, placeholder = "Select supplier", required = false }: SupplierDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [suppliers, setSuppliers] = useState<string[]>(defaultSuppliers);
  const [inputValue, setInputValue] = useState(value);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load suppliers from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedSuppliers = localStorage.getItem('suppliers');
      if (storedSuppliers) {
        try {
          const parsed = JSON.parse(storedSuppliers);
          setSuppliers([...new Set([...defaultSuppliers, ...parsed])]);
        } catch (error) {
          console.error('Error loading suppliers:', error);
        }
      }
    }
  }, []);

  // Update input value when value prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Save suppliers to localStorage
  const saveSuppliers = (updatedSuppliers: string[]) => {
    if (typeof window !== 'undefined') {
      const customSuppliers = updatedSuppliers.filter(s => !defaultSuppliers.includes(s));
      localStorage.setItem('suppliers', JSON.stringify(customSuppliers));
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        // If input has a value that's not in the list, add it as a new supplier
        if (inputValue.trim() && !suppliers.includes(inputValue.trim())) {
          const newSuppliers = [...suppliers, inputValue.trim()];
          setSuppliers(newSuppliers);
          saveSuppliers(newSuppliers);
        }
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [suppliers, inputValue]);

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setSearchTerm(newValue);
    setIsOpen(true);
    onChange(newValue);
  };

  const handleSupplierSelect = (supplier: string) => {
    setInputValue(supplier);
    onChange(supplier);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (filteredSuppliers.length > 0) {
        handleSupplierSelect(filteredSuppliers[0]);
      } else if (inputValue.trim()) {
        // Add new supplier
        const newSupplier = inputValue.trim();
        if (!suppliers.includes(newSupplier)) {
          const newSuppliers = [...suppliers, newSupplier];
          setSuppliers(newSuppliers);
          saveSuppliers(newSuppliers);
        }
        setIsOpen(false);
        setSearchTerm('');
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false);
      setSearchTerm('');
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          required={required}
          className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
        />
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 hover:text-gray-600"
        >
          <i className={`ri-arrow-${isOpen ? 'up' : 'down'}-s-line w-4 h-4 flex items-center justify-center`}></i>
        </button>
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {filteredSuppliers.length > 0 ? (
            <div className="py-1">
              {filteredSuppliers.map((supplier, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleSupplierSelect(supplier)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:outline-none focus:bg-blue-50 focus:text-blue-700"
                >
                  <div className="flex items-center justify-between">
                    <span>{supplier}</span>
                    {supplier === value && (
                      <i className="ri-check-line w-4 h-4 flex items-center justify-center text-blue-600"></i>
                    )}
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="py-2">
              {searchTerm.trim() ? (
                <button
                  type="button"
                  onClick={() => {
                    const newSupplier = searchTerm.trim();
                    if (!suppliers.includes(newSupplier)) {
                      const newSuppliers = [...suppliers, newSupplier];
                      setSuppliers(newSuppliers);
                      saveSuppliers(newSuppliers);
                    }
                    handleSupplierSelect(newSupplier);
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-blue-600 hover:bg-blue-50 focus:outline-none focus:bg-blue-50"
                >
                  <div className="flex items-center space-x-2">
                    <i className="ri-add-line w-4 h-4 flex items-center justify-center"></i>
                    <span>Add "{searchTerm.trim()}" as new supplier</span>
                  </div>
                </button>
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500">
                  No suppliers found. Start typing to add a new supplier.
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}