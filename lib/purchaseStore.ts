import { useState, useEffect } from 'react';

interface Product {
  name: string;
  sku: string;
  unitPrice: string;
  quantity: number;
  total: string;
  photo?: string;
  customData?: Record<string, any>;
}

interface CustomColumn {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'select';
  required: boolean;
  options?: string[];
}

interface Purchase {
  id: string;
  supplier: string;
  buyer: string;
  purchaseDate: string;
  total: string;
  status: string;
  products: Product[];
  customColumns?: CustomColumn[];
  deliveryDate?: string;
}

const initialPurchases: Purchase[] = [
  {
    id: 'PO-2025-001',
    supplier: 'TechSource Ltd',
    buyer: '<PERSON>',
    purchaseDate: '2025-01-15',
    total: '$3,450.00',
    status: 'Approved',
    deliveryDate: '2025-01-25',
    products: [
      { name: 'Laptop Computer', sku: 'LAP-001', unitPrice: '$899.99', quantity: 2, total: '$1,799.98', photo: 'https://readdy.ai/api/search-image?query=modern%20laptop%20computer%20silver%20professional%20business%20white%20background%20product%20photography&width=300&height=200&seq=lap001&orientation=landscape' },
      { name: 'Wireless Mouse Set', sku: 'WMS-002', unitPrice: '$45.99', quantity: 5, total: '$229.95', photo: 'https://readdy.ai/api/search-image?query=wireless%20mouse%20set%20black%20modern%20office%20equipment%20white%20background%20product%20photography&width=300&height=200&seq=wms002&orientation=landscape' },
      { name: 'USB-C Hub', sku: 'UCH-003', unitPrice: '$89.99', quantity: 3, total: '$269.97', photo: 'https://readdy.ai/api/search-image?query=USB-C%20hub%20connector%20modern%20gray%20technology%20accessory%20white%20background%20product%20photography&width=300&height=200&seq=uch003&orientation=landscape' },
      { name: 'Monitor Stand Pro', sku: 'MSP-004', unitPrice: '$149.99', quantity: 4, total: '$599.96', photo: 'https://readdy.ai/api/search-image?query=monitor%20stand%20aluminum%20professional%20office%20desk%20accessory%20modern%20white%20background%20product%20photography&width=300&height=200&seq=msp004&orientation=landscape' },
      { name: 'Keyboard Mechanical', sku: 'KBM-005', unitPrice: '$179.99', quantity: 3, total: '$539.97', photo: 'https://readdy.ai/api/search-image?query=mechanical%20keyboard%20black%20professional%20gaming%20office%20white%20background%20product%20photography&width=300&height=200&seq=kbm005&orientation=landscape' }
    ]
  },
  {
    id: 'PO-2025-002',
    supplier: 'Global Supplies Inc',
    buyer: 'Sarah Wilson',
    purchaseDate: '2025-01-18',
    total: '$2,890.00',
    status: 'Ordered',
    deliveryDate: '2025-02-01',
    products: [
      { name: 'Office Chair Ergonomic', sku: 'OCE-001', unitPrice: '$299.99', quantity: 4, total: '$1,199.96', photo: 'https://readdy.ai/api/search-image?query=ergonomic%20office%20chair%20black%20professional%20business%20furniture%20white%20background%20product%20photography&width=300&height=200&seq=oce001&orientation=landscape' },
      { name: 'Desk Lamp LED', sku: 'DLL-002', unitPrice: '$79.99', quantity: 6, total: '$479.94', photo: 'https://readdy.ai/api/search-image?query=LED%20desk%20lamp%20modern%20white%20adjustable%20office%20lighting%20white%20background%20product%20photography&width=300&height=200&seq=dll002&orientation=landscape' },
      { name: 'Cable Organizer', sku: 'CO-003', unitPrice: '$24.99', quantity: 10, total: '$249.90', photo: 'https://readdy.ai/api/search-image?query=cable%20organizer%20desk%20management%20black%20modern%20office%20accessory%20white%20background%20product%20photography&width=300&height=200&seq=co003&orientation=landscape' },
      { name: 'Whiteboard Magnetic', sku: 'WBM-004', unitPrice: '$189.99', quantity: 2, total: '$379.98', photo: 'https://readdy.ai/api/search-image?query=magnetic%20whiteboard%20white%20frame%20office%20presentation%20board%20clean%20white%20background%20product%20photography&width=300&height=200&seq=wbm004&orientation=landscape' },
      { name: 'Storage Cabinet', sku: 'SC-005', unitPrice: '$289.99', quantity: 2, total: '$579.98', photo: 'https://readdy.ai/api/search-image?query=office%20storage%20cabinet%20white%20modern%20professional%20furniture%20white%20background%20product%20photography&width=300&height=200&seq=sc005&orientation=landscape' }
    ]
  },
  {
    id: 'PO-2025-003',
    supplier: 'Premium Materials Co',
    buyer: 'Mike Johnson',
    purchaseDate: '2025-01-20',
    total: '$1,750.00',
    status: 'Pending',
    deliveryDate: '2025-02-10',
    products: [
      { name: 'Printer Multifunction', sku: 'PMF-001', unitPrice: '$349.99', quantity: 2, total: '$699.98', photo: 'https://readdy.ai/api/search-image?query=multifunction%20printer%20white%20office%20equipment%20professional%20business%20white%20background%20product%20photography&width=300&height=200&seq=pmf001&orientation=landscape' },
      { name: 'Paper A4 Premium', sku: 'PAP-002', unitPrice: '$15.99', quantity: 20, total: '$319.80', photo: 'https://readdy.ai/api/search-image?query=A4%20paper%20stack%20white%20office%20supplies%20professional%20clean%20white%20background%20product%20photography&width=300&height=200&seq=pap002&orientation=landscape' },
      { name: 'Ink Cartridge Set', sku: 'ICS-003', unitPrice: '$89.99', quantity: 5, total: '$449.95', photo: 'https://readdy.ai/api/search-image?query=ink%20cartridge%20set%20multicolor%20printer%20supplies%20professional%20white%20background%20product%20photography&width=300&height=200&seq=ics003&orientation=landscape' },
      { name: 'File Organizer', sku: 'FO-004', unitPrice: '$39.99', quantity: 7, total: '$279.93', photo: 'https://readdy.ai/api/search-image?query=file%20organizer%20desk%20office%20supplies%20black%20modern%20professional%20white%20background%20product%20photography&width=300&height=200&seq=fo004&orientation=landscape' }
    ]
  }
];

class PurchaseStore {
  private static instance: PurchaseStore;
  private purchases: Purchase[] = [];
  private listeners: (() => void)[] = [];

  private loadFromStorage(): void {
    if (typeof window !== 'undefined') {
      const storedData = localStorage.getItem('purchaseData');
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);
          this.purchases = parsedData;
        } catch (error) {
          console.error('Error loading purchase data:', error);
          this.purchases = [...initialPurchases];
        }
      } else {
        this.purchases = [...initialPurchases];
        this.saveToStorage();
      }
    }
  }

  private constructor() {
    this.purchases = [...initialPurchases];
    this.loadFromStorage();
  }

  static getInstance(): PurchaseStore {
    if (!PurchaseStore.instance) {
      PurchaseStore.instance = new PurchaseStore();
    }
    return PurchaseStore.instance;
  }

  getPurchases(): Purchase[] {
    return [...this.purchases];
  }

  getPurchaseById(purchaseId: string): Purchase | undefined {
    return this.purchases.find(purchase => purchase.id === purchaseId);
  }

  updatePurchase(purchaseId: string, updatedPurchase: Partial<Purchase>): void {
    const purchaseIndex = this.purchases.findIndex(purchase => purchase.id === purchaseId);
    if (purchaseIndex !== -1) {
      this.purchases[purchaseIndex] = { ...this.purchases[purchaseIndex], ...updatedPurchase };
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  updateProduct(purchaseId: string, productIndex: number, updatedProduct: Partial<Product>): void {
    const purchaseIndex = this.purchases.findIndex(purchase => purchase.id === purchaseId);
    if (purchaseIndex !== -1 && this.purchases[purchaseIndex].products[productIndex]) {
      this.purchases[purchaseIndex].products[productIndex] = {
        ...this.purchases[purchaseIndex].products[productIndex],
        ...updatedProduct
      };
      
      // Recalculate total if price or quantity changed
      if (updatedProduct.unitPrice || updatedProduct.quantity) {
        const product = this.purchases[purchaseIndex].products[productIndex];
        const unitPrice = parseFloat(product.unitPrice.replace(/[$,]/g, ''));
        const quantity = product.quantity;
        const total = unitPrice * quantity;
        this.purchases[purchaseIndex].products[productIndex].total = `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        
        // Recalculate purchase total
        const purchaseTotal = this.purchases[purchaseIndex].products.reduce((sum, prod) => {
          return sum + parseFloat(prod.total.replace(/[$,]/g, ''));
        }, 0);
        this.purchases[purchaseIndex].total = `$${purchaseTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      }
      
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  addPurchase(purchaseData: {
    supplier: string;
    buyer: string;
    deliveryDate: string;
    status: string;
    products: Array<{
      name: string;
      unitPrice: string;
      quantity: string;
      photo?: string;
      customData?: Record<string, any>;
    }>;
    customColumns?: CustomColumn[];
  }): Purchase {
    try {
      // Validate required fields
      if (!purchaseData.supplier || !purchaseData.buyer) {
        throw new Error('Supplier and Buyer are required');
      }

      if (!purchaseData.products || purchaseData.products.length === 0) {
        throw new Error('At least one product is required');
      }

      // Validate and process products
      const products: Product[] = purchaseData.products.map((product, index) => {
        if (!product.name || !product.name.trim()) {
          throw new Error(`Product ${index + 1}: Name is required`);
        }

        const unitPrice = parseFloat(product.unitPrice) || 0;
        const quantity = parseInt(product.quantity) || 0;
        
        if (unitPrice <= 0) {
          throw new Error(`Product ${index + 1}: Unit price must be greater than 0`);
        }
        
        if (quantity <= 0) {
          throw new Error(`Product ${index + 1}: Quantity must be greater than 0`);
        }

        const total = unitPrice * quantity;
        
        return {
          name: product.name.trim(),
          sku: `SKU-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
          unitPrice: `$${unitPrice.toFixed(2)}`,
          quantity: quantity,
          total: `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
          photo: product.photo || '',
          customData: product.customData || {}
        };
      });

      // Calculate totals
      const total = products.reduce((sum, product) => {
        return sum + parseFloat(product.total.replace(/[$,]/g, ''));
      }, 0);

      // Generate unique purchase ID
      const purchaseId = `PO-${new Date().getFullYear()}-${String(this.purchases.length + 1).padStart(3, '0')}`;

      // Create new purchase object
      const newPurchase: Purchase = {
        id: purchaseId,
        supplier: purchaseData.supplier.trim(),
        buyer: purchaseData.buyer.trim(),
        purchaseDate: new Date().toISOString().split('T')[0],
        total: `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        status: purchaseData.status || 'Pending',
        deliveryDate: purchaseData.deliveryDate || '',
        products: products,
        customColumns: purchaseData.customColumns || []
      };

      // Add to purchases array
      this.purchases.unshift(newPurchase);
      
      // Save to storage
      this.saveToStorage();
      
      // Notify listeners
      this.notifyListeners();

      // Return the created purchase
      return newPurchase;

    } catch (error) {
      console.error('Error in addPurchase:', error);
      throw error; // Re-throw the error so it can be caught by the UI
    }
  }

  updatePurchaseStatus(purchaseId: string, newStatus: string): void {
    const purchaseIndex = this.purchases.findIndex(purchase => purchase.id === purchaseId);
    if (purchaseIndex !== -1) {
      this.purchases[purchaseIndex].status = newStatus;
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  updateCustomColumns(purchaseId: string, customColumns: CustomColumn[]): void {
    const purchaseIndex = this.purchases.findIndex(purchase => purchase.id === purchaseId);
    if (purchaseIndex !== -1) {
      this.purchases[purchaseIndex].customColumns = customColumns;
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  addCustomColumn(purchaseId: string, column: CustomColumn): void {
    const purchaseIndex = this.purchases.findIndex(purchase => purchase.id === purchaseId);
    if (purchaseIndex !== -1) {
      if (!this.purchases[purchaseIndex].customColumns) {
        this.purchases[purchaseIndex].customColumns = [];
      }
      this.purchases[purchaseIndex].customColumns.push(column);
      
      // Initialize custom data for existing products
      this.purchases[purchaseIndex].products.forEach(product => {
        if (!product.customData) product.customData = {};
        product.customData[column.id] = '';
      });
      
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  removeCustomColumn(purchaseId: string, columnId: string): void {
    const purchaseIndex = this.purchases.findIndex(purchase => purchase.id === purchaseId);
    if (purchaseIndex !== -1) {
      if (this.purchases[purchaseIndex].customColumns) {
        this.purchases[purchaseIndex].customColumns = this.purchases[purchaseIndex].customColumns.filter(col => col.id !== columnId);
      }
      
      // Remove custom data from products
      this.purchases[purchaseIndex].products.forEach(product => {
        if (product.customData) {
          delete product.customData[columnId];
        }
      });
      
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  private saveToStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        const dataToSave = JSON.stringify(this.purchases);
        localStorage.setItem('purchaseData', dataToSave);
      }
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      // Don't throw error, just log it to prevent form submission failure
    }
  }

  // Listener management
  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }
}

export default PurchaseStore;
export type { Purchase, Product, CustomColumn };