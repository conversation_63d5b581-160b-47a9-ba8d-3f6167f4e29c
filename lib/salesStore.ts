
import { useState, useEffect } from 'react';

interface Product {
  name: string;
  sku: string;
  unitPrice: string;
  quantity: number;
  total: string;
  photo?: string;
  customData?: Record<string, any>;
}

interface CustomColumn {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'select';
  required: boolean;
  options?: string[];
}

interface Sale {
  id: string;
  vendor: string;
  customer: string;
  salesDate: string;
  total: string;
  commission: string;
  status: string;
  products: Product[];
  customColumns?: CustomColumn[];
  deliveryDate?: string;
  salesRep?: string;
}

export interface Payment {
  id: string;
  orderId: string;
  customerName: string;
  amount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentMethod: 'Cash' | 'Credit Card' | 'Bank Transfer' | 'Check' | 'PayPal';
  status: 'Paid' | 'Partial' | 'Pending' | 'Overdue';
  dueDate: string;
  paidDate?: string;
  invoiceNumber: string;
  notes?: string;
}

export interface AdvancePayment {
  id: string;
  customerName: string;
  amount: number;
  paymentMethod: 'Cash' | 'Credit Card' | 'Bank Transfer' | 'Check' | 'PayPal';
  date: string;
  referenceNumber: string;
  notes?: string;
  status: 'Active' | 'Applied';
  appliedToInvoices?: string[];
}

const initialSales: Sale[] = [
  {
    id: 'SO-2024-001',
    vendor: 'TechSource Ltd',
    customer: 'Alice Johnson',
    salesDate: '2024-01-15',
    total: '$2,450.00',
    commission: '$245.00',
    status: 'Completed',
    salesRep: '<PERSON>',
    deliveryDate: '2024-01-20',
    products: [
      { name: 'Wireless Mouse', sku: 'WM-001', unitPrice: '$29.99', quantity: 5, total: '$149.95', photo: 'https://readdy.ai/api/search-image?query=modern%20wireless%20computer%20mouse%20sleek%20design%20white%20background%20product%20photography%20high%20quality%20detailed&width=300&height=200&seq=wm001&orientation=landscape' },
      { name: 'USB Cable', sku: 'UC-101', unitPrice: '$12.50', quantity: 10, total: '$125.00', photo: 'https://readdy.ai/api/search-image?query=USB%20cable%20connector%20black%20modern%20technology%20product%20photography%20clean%20white%20background%20detailed&width=300&height=200&seq=uc101&orientation=landscape' },
      { name: 'Monitor Stand', sku: 'MS-201', unitPrice: '$89.99', quantity: 3, total: '$269.97', photo: 'https://readdy.ai/api/search-image?query=computer%20monitor%20stand%20aluminum%20modern%20office%20desk%20accessory%20white%20background%20product%20photography&width=300&height=200&seq=ms201&orientation=landscape' },
      { name: 'Keyboard', sku: 'KB-301', unitPrice: '$125.99', quantity: 8, total: '$1,007.92', photo: 'https://readdy.ai/api/search-image?query=mechanical%20keyboard%20modern%20black%20keys%20professional%20office%20equipment%20white%20background%20product%20photography&width=300&height=200&seq=kb301&orientation=landscape' },
      { name: 'Webcam HD', sku: 'WC-401', unitPrice: '$179.99', quantity: 5, total: '$899.95', photo: 'https://readdy.ai/api/search-image?query=HD%20webcam%20modern%20black%20professional%20video%20conference%20camera%20white%20background%20product%20photography&width=300&height=200&seq=wc401&orientation=landscape' }
    ]
  },
  {
    id: 'SO-2025-005',
    vendor: 'Digital Solutions',
    customer: 'Tech Innovators Inc',
    salesDate: '2025-01-22',
    total: '$3,750.00',
    commission: '$375.00',
    status: 'Processing',
    salesRep: 'Mike Rodriguez',
    deliveryDate: '2025-02-10',
    products: [
      { name: 'Smart Tablet', sku: 'ST-001', unitPrice: '$599.99', quantity: 3, total: '$1,799.97', photo: 'https://readdy.ai/api/search-image?query=modern%20tablet%20device%20sleek%20black%20professional%20technology%20white%20background%20product%20photography&width=300&height=200&seq=st001&orientation=landscape' },
      { name: 'Wireless Charger', sku: 'WC-002', unitPrice: '$89.99', quantity: 5, total: '$449.95', photo: 'https://readdy.ai/api/search-image?query=wireless%20charging%20pad%20modern%20black%20technology%20accessory%20white%20background%20product%20photography&width=300&height=200&seq=wc002&orientation=landscape' },
      { name: 'Bluetooth Speaker', sku: 'BS-003', unitPrice: '$149.99', quantity: 4, total: '$599.96', photo: 'https://readdy.ai/api/search-image?query=bluetooth%20speaker%20portable%20modern%20black%20audio%20device%20white%20background%20product%20photography&width=300&height=200&seq=bs003&orientation=landscape' },
      { name: 'Phone Case', sku: 'PC-004', unitPrice: '$29.99', quantity: 10, total: '$299.90', photo: 'https://readdy.ai/api/search-image?query=phone%20case%20protective%20black%20modern%20smartphone%20accessory%20white%20background%20product%20photography&width=300&height=200&seq=pc004&orientation=landscape' },
      { name: 'Screen Protector', sku: 'SP-005', unitPrice: '$19.99', quantity: 30, total: '$599.70', photo: 'https://readdy.ai/api/search-image?query=screen%20protector%20glass%20clear%20smartphone%20protection%20white%20background%20product%20photography&width=300&height=200&seq=sp005&orientation=landscape' }
    ]
  },
  {
    id: 'SO-2025-014',
    vendor: 'CMK Solutions',
    customer: 'CMK',
    salesDate: '2025-01-20',
    total: '$4,200.00',
    commission: '$420.00',
    status: 'Confirmed',
    salesRep: 'Alex Johnson',
    deliveryDate: '2025-01-30',
    products: [
      { name: 'Professional Monitor', sku: 'PM-001', unitPrice: '$899.99', quantity: 2, total: '$1,799.98', photo: 'https://readdy.ai/api/search-image?query=professional%20computer%20monitor%204K%20display%20modern%20black%20sleek%20design%20white%20background%20product%20photography&width=300&height=200&seq=pm001&orientation=landscape' },
      { name: 'Ergonomic Keyboard', sku: 'EK-002', unitPrice: '$249.99', quantity: 3, total: '$749.97', photo: 'https://readdy.ai/api/search-image?query=ergonomic%20keyboard%20wireless%20modern%20professional%20office%20white%20background%20product%20photography&width=300&height=200&seq=ek002&orientation=landscape' },
      { name: 'Wireless Presenter', sku: 'WP-003', unitPrice: '$149.99', quantity: 4, total: '$599.96', photo: 'https://readdy.ai/api/search-image?query=wireless%20presenter%20remote%20laser%20pointer%20professional%20presentation%20tool%20white%20background%20product%20photography&width=300&height=200&seq=wp003&orientation=landscape' },
      { name: 'USB Hub', sku: 'UH-004', unitPrice: '$79.99', quantity: 6, total: '$479.94', photo: 'https://readdy.ai/api/search-image?query=USB%20hub%20multi%20port%20connector%20modern%20black%20technology%20accessory%20white%20background%20product%20photography&width=300&height=200&seq=uh004&orientation=landscape' },
      { name: 'Cable Management', sku: 'CM-005', unitPrice: '$39.99', quantity: 14, total: '$559.86', photo: 'https://readdy.ai/api/search-image?query=cable%20management%20system%20office%20desk%20organizer%20black%20modern%20professional%20white%20background%20product%20photography&width=300&height=200&seq=cm005&orientation=landscape' }
    ]
  },
  {
    id: 'SO-2025-015',
    vendor: 'KMS Technologies',
    customer: 'KMS',
    salesDate: '2025-01-18',
    total: '$5,850.00',
    commission: '$585.00',
    status: 'Processing',
    salesRep: 'Sarah Wilson',
    deliveryDate: '2025-02-05',
    products: [
      { name: 'Server Monitor', sku: 'SM-001', unitPrice: '$1,299.99', quantity: 3, total: '$3,899.97', photo: 'https://readdy.ai/api/search-image?query=server%20monitor%20large%20display%20professional%20black%20modern%20technology%20white%20background%20product%20photography&width=300&height=200&seq=sm001&orientation=landscape' },
      { name: 'Network Switch', sku: 'NS-002', unitPrice: '$899.99', quantity: 2, total: '$1,799.98', photo: 'https://readdy.ai/api/search-image?query=network%20switch%20ethernet%20professional%20IT%20equipment%20black%20modern%20white%20background%20product%20photography&width=300&height=200&seq=ns002&orientation=landscape' },
      { name: 'Wireless Access Point', sku: 'WAP-003', unitPrice: '$149.99', quantity: 1, total: '$149.99', photo: 'https://readdy.ai/api/search-image?query=wireless%20access%20point%20router%20white%20modern%20network%20equipment%20professional%20white%20background%20product%20photography&width=300&height=200&seq=wap003&orientation=landscape' }
    ]
  }
];

class SalesStore {
  private static instance: SalesStore;
  private sales: Sale[] = [];
  private listeners: (() => void)[] = [];

  payments: Payment[] = [
    {
      id: 'PAY-001',
      orderId: 'SO-2025-014',
      customerName: 'CMK',
      amount: 4200,
      paidAmount: 0,
      remainingAmount: 4200,
      paymentMethod: 'Bank Transfer',
      status: 'Pending',
      dueDate: '2025-02-20',
      invoiceNumber: 'INV-2025-014'
    },
    {
      id: 'PAY-002',
      orderId: 'SO-2025-013',
      customerName: 'Sarah Wilson',
      amount: 2800,
      paidAmount: 2800,
      remainingAmount: 0,
      paymentMethod: 'Credit Card',
      status: 'Paid',
      dueDate: '2025-09-15',
      paidDate: '2025-09-14',
      invoiceNumber: 'INV-2025-013'
    },
    {
      id: 'PAY-003',
      orderId: 'SO-2025-012',
      customerName: 'John Smith',
      amount: 3200,
      paidAmount: 0,
      remainingAmount: 3200,
      paymentMethod: 'Bank Transfer',
      status: 'Overdue',
      dueDate: '2025-09-10',
      invoiceNumber: 'INV-2025-012',
      notes: 'Customer requested extension'
    },
    {
      id: 'PAY-004',
      orderId: 'SO-2025-011',
      customerName: 'Lisa Chen',
      amount: 1500,
      paidAmount: 0,
      remainingAmount: 1500,
      paymentMethod: 'Check',
      status: 'Pending',
      dueDate: '2025-09-25',
      invoiceNumber: 'INV-2025-011'
    }
  ];

  advancePayments: AdvancePayment[] = [
    {
      id: 'ADV-001',
      customerName: 'CMK',
      amount: 1500,
      paymentMethod: 'Bank Transfer',
      date: '2025-01-15',
      referenceNumber: 'ADV-2025-001',
      notes: 'Advance payment for upcoming orders',
      status: 'Active',
      appliedToInvoices: []
    },
    {
      id: 'ADV-002',
      customerName: 'Sarah Wilson',
      amount: 800,
      paymentMethod: 'Credit Card',
      date: '2025-01-18',
      referenceNumber: 'ADV-2025-002',
      notes: 'Prepayment for future services',
      status: 'Active',
      appliedToInvoices: []
    }
  ];

  private loadFromStorage(): void {
    if (typeof window !== 'undefined') {
      const storedData = localStorage.getItem('salesData');
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);
          this.sales = parsedData;
        } catch (error) {
          console.error('Error loading sales data:', error);
          this.sales = [...initialSales];
        }
      } else {
        this.sales = [...initialSales];
        this.saveToStorage();
      }
    }
  }

  private loadPaymentsFromStorage(): void {
    if (typeof window !== 'undefined') {
      const storedPayments = localStorage.getItem('paymentsData');
      if (storedPayments) {
        try {
          this.payments = JSON.parse(storedPayments);
        } catch (error) {
          console.error('Error loading payments:', error);
        }
      }
    }
  }

  private loadAdvancePaymentsFromStorage(): void {
    if (typeof window !== 'undefined') {
      const storedAdvancePayments = localStorage.getItem('advancePaymentsData');
      if (storedAdvancePayments) {
        try {
          this.advancePayments = JSON.parse(storedAdvancePayments);
        } catch (error) {
          console.error('Error loading advance payments:', error);
        }
      }
    }
  }

  private constructor() {
    this.sales = [...initialSales];
    this.loadFromStorage();
    this.loadPaymentsFromStorage();
    this.loadAdvancePaymentsFromStorage();
  }

  static getInstance(): SalesStore {
    if (!SalesStore.instance) {
      SalesStore.instance = new SalesStore();
    }
    return SalesStore.instance;
  }

  getSales(): Sale[] {
    return [...this.sales];
  }

  getSaleById(saleId: string): Sale | undefined {
    return this.sales.find(sale => sale.id === saleId);
  }

  updateSale(saleId: string, updatedSale: Partial<Sale>): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1) {
      this.sales[saleIndex] = { ...this.sales[saleIndex], ...updatedSale };
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  updateProduct(saleId: string, productIndex: number, updatedProduct: Partial<Product>): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1 && this.sales[saleIndex].products[productIndex]) {
      this.sales[saleIndex].products[productIndex] = {
        ...this.sales[saleIndex].products[productIndex],
        ...updatedProduct
      };
      
      // Recalculate total if price or quantity changed
      if (updatedProduct.unitPrice || updatedProduct.quantity) {
        const product = this.sales[saleIndex].products[productIndex];
        const unitPrice = parseFloat(product.unitPrice.replace(/[$,]/g, ''));
        const quantity = product.quantity;
        const total = unitPrice * quantity;
        this.sales[saleIndex].products[productIndex].total = `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        
        // Recalculate sale total
        const saleTotal = this.sales[saleIndex].products.reduce((sum, prod) => {
          return sum + parseFloat(prod.total.replace(/[$,]/g, ''));
        }, 0);
        this.sales[saleIndex].total = `$${saleTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      }
      
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  addSale(saleData: {
    customer: string;
    salesRep: string;
    deliveryDate: string;
    status: string;
    products: Array<{
      name: string;
      unitPrice: string;
      quantity: string;
      photo?: string;
      customData?: Record<string, any>;
    }>;
    customColumns?: CustomColumn[];
  }): Sale {
    try {
      // Validate required fields
      if (!saleData.customer || !saleData.salesRep) {
        throw new Error('Customer and Sales Representative are required');
      }

      if (!saleData.products || saleData.products.length === 0) {
        throw new Error('At least one product is required');
      }

      // Validate and process products
      const products: Product = saleData.products.map((product, index) => {
        if (!product.name || !product.name.trim()) {
          throw new Error(`Product ${index + 1}: Name is required`);
        }

        const unitPrice = parseFloat(product.unitPrice) || 0;
        const quantity = parseInt(product.quantity) || 0;
        
        if (unitPrice <= 0) {
          throw new Error(`Product ${index + 1}: Unit price must be greater than 0`);
        }
        
        if (quantity <= 0) {
          throw new Error(`Product ${index + 1}: Quantity must be greater than 0`);
        }

        const total = unitPrice * quantity;
        
        return {
          name: product.name.trim(),
          sku: `SKU-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
          unitPrice: `$${unitPrice.toFixed(2)}`,
          quantity: quantity,
          total: `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
          photo: product.photo || '',
          customData: product.customData || {}
        };
      });

      // Calculate totals
      const total = products.reduce((sum, product) => {
        return sum + parseFloat(product.total.replace(/[$,]/g, ''));
      }, 0);

      const commission = total * 0.1;

      // Generate unique sale ID
      const saleId = `SO-${new Date().getFullYear()}-${String(this.sales.length + 1).padStart(3, '0')}`;

      // Create new sale object
      const newSale: Sale = {
        id: saleId,
        vendor: saleData.salesRep.trim(),
        customer: saleData.customer.trim(),
        salesDate: new Date().toISOString().split('T')[0],
        total: `$${total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        commission: `$${commission.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
        status: saleData.status || 'Pending',
        salesRep: saleData.salesRep.trim(),
        deliveryDate: saleData.deliveryDate || '',
        products: products,
        customColumns: saleData.customColumns || []
      };

      // Add to sales array
      this.sales.unshift(newSale);
      
      // Save to storage
      this.saveToStorage();
      
      // Notify listeners
      this.notifyListeners();

      // Return the created sale
      return newSale;

    } catch (error) {
      console.error('Error in addSale:', error);
      throw error; // Re-throw the error so it can be caught by the UI
    }
  }

  updateSaleStatus(saleId: string, newStatus: string): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1) {
      this.sales[saleIndex].status = newStatus;
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  updateSaleCommission(saleId: string, newCommission: string): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1) {
      this.sales[saleIndex].commission = newCommission;
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  updateCustomColumns(saleId: string, customColumns: CustomColumn[]): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1) {
      this.sales[saleIndex].customColumns = customColumns;
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  addCustomColumn(saleId: string, column: CustomColumn): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1) {
      if (!this.sales[saleIndex].customColumns) {
        this.sales[saleIndex].customColumns = [];
      }
      this.sales[saleIndex].customColumns.push(column);
      
      // Initialize custom data for existing products
      this.sales[saleIndex].products.forEach(product => {
        if (!product.customData) product.customData = {};
        product.customData[column.id] = '';
      });
      
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  removeCustomColumn(saleId: string, columnId: string): void {
    const saleIndex = this.sales.findIndex(sale => sale.id === saleId);
    if (saleIndex !== -1) {
      if (this.sales[saleIndex].customColumns) {
        this.sales[saleIndex].customColumns = this.sales[saleIndex].customColumns.filter(col => col.id !== columnId);
      }
      
      // Remove custom data from products
      this.sales[saleIndex].products.forEach(product => {
        if (product.customData) {
          delete product.customData[columnId];
        }
      });
      
      this.saveToStorage();
      this.notifyListeners();
    }
  }

  getPaymentsByCustomer(customerName: string): Payment[] {
    return this.payments.filter(payment => payment.customerName === customerName);
  }

  addPayment(payment: Omit<Payment, 'id'>): void {
    const newPayment: Payment = {
      ...payment,
      id: `PAY-${String(this.payments.length + 1).padStart(3, '0')}`
    };
    this.payments.unshift(newPayment);
    this.savePaymentsToStorage();
    this.notifyListeners();
  }

  updatePayment(paymentId: string, updates: Partial<Payment>): void {
    const paymentIndex = this.payments.findIndex(p => p.id === paymentId);
    if (paymentIndex !== -1) {
      this.payments[paymentIndex] = { ...this.payments[paymentIndex], ...updates };
    }
  }

  recordPayment(paymentId: string, amount: number, method: Payment['paymentMethod'], notes?: string): void {
    const payment = this.payments.find(p => p.id === paymentId);
    if (payment) {
      const newPaidAmount = payment.paidAmount + amount;
      const remainingAmount = payment.amount - newPaidAmount;
      
      this.updatePayment(paymentId, {
        paidAmount: newPaidAmount,
        remainingAmount: remainingAmount,
        status: remainingAmount <= 0 ? 'Paid' : 'Partial',
        paidDate: new Date().toISOString().split('T')[0],
        paymentMethod: method,
        notes: notes
      });
      
      this.savePaymentsToStorage();
      this.notifyListeners();
    }
  }

  createInvoiceFromSale(saleId: string, dueDate: string): Payment {
    const sale = this.getSaleById(saleId);
    if (!sale) {
      throw new Error('Sale not found');
    }

    const amount = parseFloat(sale.total.replace(/[$,]/g, ''));
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(this.payments.length + 1).padStart(3, '0')}`;
    
    const newPayment: Payment = {
      id: `PAY-${String(this.payments.length + 1).padStart(3, '0')}`,
      orderId: sale.id,
      customerName: sale.customer,
      amount: amount,
      paidAmount: 0,
      remainingAmount: amount,
      paymentMethod: 'Bank Transfer',
      status: 'Pending',
      dueDate: dueDate,
      invoiceNumber: invoiceNumber
    };

    this.payments.unshift(newPayment);
    this.savePaymentsToStorage();
    this.notifyListeners();
    
    return newPayment;
  }

  private savePaymentsToStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        const dataToSave = JSON.stringify(this.payments);
        localStorage.setItem('paymentsData', dataToSave);
      }
    } catch (error) {
      console.error('Error saving payments to localStorage:', error);
      // Don't throw error, just log it to prevent form submission failure
    }
  }

  private saveToStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        const dataToSave = JSON.stringify(this.sales);
        localStorage.setItem('salesData', dataToSave);
      }
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      // Don't throw error, just log it to prevent form submission failure
    }
  }

  // Advance Payments Management

  getAdvancePaymentsByCustomer(customerName: string): AdvancePayment[] {
    return this.advancePayments.filter(advance => advance.customerName === customerName);
  }

  recordAdvancePayment(customerName: string, amount: number, method: AdvancePayment['paymentMethod'], notes?: string): AdvancePayment {
    const newAdvancePayment: AdvancePayment = {
      id: `ADV-${String(this.advancePayments.length + 1).padStart(3, '0')}`,
      customerName: customerName,
      amount: amount,
      paymentMethod: method,
      date: new Date().toISOString().split('T')[0],
      referenceNumber: `ADV-${new Date().getFullYear()}-${String(this.advancePayments.length + 1).padStart(3, '0')}`,
      notes: notes,
      status: 'Active',
      appliedToInvoices: []
    };

    this.advancePayments.unshift(newAdvancePayment);
    this.saveAdvancePaymentsToStorage();
    this.notifyListeners();
    
    return newAdvancePayment;
  }

  applyAdvancePaymentToInvoice(advancePaymentId: string, invoiceId: string, amount: number): void {
    const advancePayment = this.advancePayments.find(adv => adv.id === advancePaymentId);
    if (advancePayment && advancePayment.amount >= amount) {
      advancePayment.amount -= amount;
      if (!advancePayment.appliedToInvoices) {
        advancePayment.appliedToInvoices = [];
      }
      advancePayment.appliedToInvoices.push(invoiceId);
      
      if (advancePayment.amount <= 0) {
        advancePayment.status = 'Applied';
      }
      
      this.saveAdvancePaymentsToStorage();
      this.notifyListeners();
    }
  }

  private saveAdvancePaymentsToStorage(): void {
    try {
      if (typeof window !== 'undefined') {
        const dataToSave = JSON.stringify(this.advancePayments);
        localStorage.setItem('advancePaymentsData', dataToSave);
      }
    } catch (error) {
      console.error('Error saving advance payments to localStorage:', error);
      // Don't throw error, just log it to prevent form submission failure
    }
  }

  // Listener management

  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }
}

// Sales Store Hook Interface
interface SalesStoreState {
  payments: Payment[];
  advancePayments: AdvancePayment[];
  getPaymentsByCustomer: (customerName: string) => Payment[];
  getAdvancePaymentsByCustomer: (customerName: string) => AdvancePayment[];
  addPayment: (payment: Omit<Payment, 'id'>) => void;
  updatePayment: (paymentId: string, updates: Partial<Payment>) => void;
  recordPayment: (paymentId: string, amount: number, method: Payment['paymentMethod'], notes?: string) => void;
  recordAdvancePayment: (customerName: string, amount: number, method: AdvancePayment['paymentMethod'], notes?: string) => AdvancePayment;
  applyAdvancePaymentToInvoice: (advancePaymentId: string, invoiceId: string, amount: number) => void;
  createInvoiceFromSale: (saleId: string, dueDate: string) => Payment;
}

// React Hook for SalesStore
export function useSalesStore<T>(selector: (state: SalesStoreState) => T): T {
  const [state, setState] = useState<SalesStoreState>(() => {
    const store = SalesStore.getInstance();
    return {
      payments: store.payments,
      advancePayments: store.advancePayments,
      getPaymentsByCustomer: (customerName: string) => store.getPaymentsByCustomer(customerName),
      getAdvancePaymentsByCustomer: (customerName: string) => store.getAdvancePaymentsByCustomer(customerName),
      addPayment: (payment: Omit<Payment, 'id'>) => store.addPayment(payment),
      updatePayment: (paymentId: string, updates: Partial<Payment>) => store.updatePayment(paymentId, updates),
      recordPayment: (paymentId: string, amount: number, method: Payment['paymentMethod'], notes?: string) => store.recordPayment(paymentId, amount, method, notes),
      recordAdvancePayment: (customerName: string, amount: number, method: AdvancePayment['paymentMethod'], notes?: string) => store.recordAdvancePayment(customerName, amount, method, notes),
      applyAdvancePaymentToInvoice: (advancePaymentId: string, invoiceId: string, amount: number) => store.applyAdvancePaymentToInvoice(advancePaymentId, invoiceId, amount),
      createInvoiceFromSale: (saleId: string, dueDate: string) => store.createInvoiceFromSale(saleId, dueDate)
    };
  });

  useEffect(() => {
    const store = SalesStore.getInstance();
    
    const updateState = () => {
      setState({
        payments: store.payments,
        advancePayments: store.advancePayments,
        getPaymentsByCustomer: (customerName: string) => store.getPaymentsByCustomer(customerName),
        getAdvancePaymentsByCustomer: (customerName: string) => store.getAdvancePaymentsByCustomer(customerName),
        addPayment: (payment: Omit<Payment, 'id'>) => store.addPayment(payment),
        updatePayment: (paymentId: string, updates: Partial<Payment>) => store.updatePayment(paymentId, updates),
        recordPayment: (paymentId: string, amount: number, method: Payment['paymentMethod'], notes?: string) => store.recordPayment(paymentId, amount, method, notes),
        recordAdvancePayment: (customerName: string, amount: number, method: AdvancePayment['paymentMethod'], notes?: string) => store.recordAdvancePayment(customerName, amount, method, notes),
        applyAdvancePaymentToInvoice: (advancePaymentId: string, invoiceId: string, amount: number) => store.applyAdvancePaymentToInvoice(advancePaymentId, invoiceId, amount),
        createInvoiceFromSale: (saleId: string, dueDate: string) => store.createInvoiceFromSale(saleId, dueDate)
      });
    };

    const unsubscribe = store.subscribe(updateState);
    return unsubscribe;
  }, []);

  return selector(state);
}

export default SalesStore;
export type { Sale, Product, CustomColumn, AdvancePayment };
